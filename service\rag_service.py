"""
RAG服务核心 - 整合知识库构建、检索和问答功能
"""

from typing import List, Dict, Any, Optional, Set
from .knowledge_builder import KnowledgeBuilder
from .tree_retriever import TreeRetriever
from .llm_service import LLMService
from config import Config

class RAGService:
    """RAG问答服务核心类"""
    
    def __init__(self,  vector_store_type: str = "faiss"):
      
  
        self.vector_store_type = vector_store_type
        
        # 初始化各服务组件
        self.knowledge_builder = KnowledgeBuilder(vector_store_type)
        self.tree_retriever = TreeRetriever()
        self.llm_service = LLMService()
        
        # 存储实例
        self.semantic_store = None
        self.node_store = None
        
        # 数据路径
        self.raw_data_path = Config.get("MS_TREE_PATH")+"/raw_data.json"
        self.synonyms_path = Config.get("MS_TREE_PATH")+"/synonyms_mapping.json"
        self.tree_structure_path = Config.get("MS_TREE_PATH")+"/tree_structure.json"
        
        # 是否已初始化
        self.initialized = False
    
    def initialize(self, rebuild_knowledge_base: bool = False, batch_size: int = 1000):
        """
        初始化RAG服务
        
        Args:
            rebuild_knowledge_base: 是否重新构建知识库
            batch_size: 批处理大小
        """
        print("=== 初始化RAG服务 ===")
        
        # 加载树检索器数据
        self.tree_retriever.load_data(self.tree_structure_path, self.raw_data_path)
        
        if rebuild_knowledge_base:
            # 重新构建知识库
            print("重新构建知识库...")
            self.semantic_store, self.node_store = self.knowledge_builder.build_all_stores(
                self.raw_data_path, 
                self.synonyms_path, 
                self.tree_structure_path,
                batch_size=batch_size
            )
        else:
            # 尝试加载已有的知识库
            print("加载已有知识库...")
            self.semantic_store, self.node_store = self.knowledge_builder.get_stores()
        
        # # 测试LLM连接
        # if self.llm_service.test_connection():
        #     print("LLM服务连接正常")
        # else:
        #     print("警告：LLM服务连接失败")
        
        self.initialized = True
        print("=== RAG服务初始化完成 ===")
    
    def query(self, user_question: str, k: int = 5) -> Dict[str, Any]:
        """
        处理用户问题并返回答案
        
        Args:
            user_question: 用户问题
            k: 检索返回的top-k结果数量
            
        Returns:
            包含答案和相关信息的字典
        """
        if not self.initialized:
            return {"error": "RAG服务未初始化，请先调用initialize()"}
        
        # print(f"\n=== 处理用户问题：{user_question} ===")
        
        try:
            # 第一步：从语义超图库检索
            # print("1. 语义超图库检索...")

            relevant_doc = []

            semantic_results = self.semantic_store.search(user_question, k=3)          
            relevant_doc.extend(doc.metadata for doc in semantic_results)
            # print(f"检索到 {len(semantic_node_ids)} 个语义节点")
            
            # 第二步：LLM抽取实体
            # print("2. 实体抽取...")
            entities = self.llm_service.extract_entities(user_question)
            # print(f"抽取到化合物：{entities['compounds']}")
            # print(f"抽取到疾病：{entities['diseases']}")
            
            # 第三步：节点库检索
            # print("3. 节点库检索...")

            # 分别检索化合物（Level 2）和疾病（Level 1），使用level过滤
            # 检索疾病（Level 1）
            for disease in entities['diseases']:
                if disease.strip():
                    disease_results = self.node_store.search(disease, k=2, level_filter=1)
                    relevant_doc.extend(doc.metadata for doc in disease_results)

                  
            # 检索化合物（Level 2）
            for compound in entities['compounds']:
                if compound.strip():
                    compound_results = self.node_store.search(compound, k=2, level_filter=2)
                    relevant_doc.extend(doc.metadata for doc in compound_results)
                   

            # 获取推理路径
            decision_path_result = self.llm_service.decision_path(user_question, relevant_doc)

            print(decision_path_result)
            
            print("--------------------------------")
            
            # 第五步：获取层级化知识
            # print("5. 构建层级化知识结构...")
            hierarchical_knowledge = self._build_hierarchical_knowledge_new(decision_path_result)
            print(hierarchical_knowledge)
            # 第六步：生成答案
            # print("6. 生成答案...")
            answer = self.llm_service.generate_answer_hierarchical(
                user_question,
                hierarchical_knowledge
            )

            
            
            # 准备返回结果
            result = {
                "question": user_question,
                "answer": answer,
                "entities": entities,
                "hierarchical_structure_count": len(hierarchical_knowledge),
                "hierarchical_knowledge_json": hierarchical_knowledge,  # 添加JSON格式的层级化知识数据
                "success": True
            }
            
            # print("=== 问答完成 ===")
            return result
            
        except Exception as e:
            print(f"问答过程中发生错误: {e}")
            return {
                "question": user_question,
                "error": str(e),
                "success": False
            }
    
     def _build_hierarchical_knowledge(self, knowledge: list) -> list:
        """
        构建层级化的知识结构：
        - level1节点：只查自己和直接下属的level2节点（1-2结构）
        - level2节点：查自己、父level1、所有下属level3节点（1-2-3结构）
        - level3节点：查自己、父level2、祖父level1（1-2-3结构）
        """
        from collections import OrderedDict
        result_dict = OrderedDict()  # id -> node结构



        for node_id in knowledge:
            node_data = self.tree_retriever.get_node_data(node_id)
            if not node_data:
                continue
            level = node_data.get('level')

            if level == 1:
                # 1-2结构
                node = node_data.copy()
                node['children'] = []
            
                for level2_id in self.tree_retriever.get_children_nodes(node_id):
                    level2_data = self.tree_retriever.get_node_data(level2_id)
                    if level2_data and level2_data.get('level') == 2:
                        level2_node = level2_data.copy()
                        level2_node['children'] = []
                        node['children'].append(level2_node)
                        result_dict[level2_id] = level2_node
                result_dict[node_id] = node
            
            elif level == 2:
                # 1-2-3结构
                level2_data = node_data.copy()
                level2_data['children'] = []
                # level3 children
                for level3_id in self.tree_retriever.get_children_nodes(node_id):
                    level3_data = self.tree_retriever.get_node_data(level3_id)
                    if level3_data and level3_data.get('level') == 3:
                        level3_node = level3_data.copy()
                        level3_node['children'] = []
                        level2_data['children'].append(level3_node)
                        result_dict[level3_id] = level3_node
                # 父level1
                parent1_id = self.tree_retriever.get_parent_node(node_id)
                if parent1_id:
                    parent1_data = self.tree_retriever.get_node_data(parent1_id)
                    if parent1_data and parent1_data.get('level') == 1:
                        parent1_node = parent1_data.copy()
                        parent1_node['children'] = [level2_data]
                        result_dict[parent1_id] = parent1_node
                result_dict[node_id] = level2_data

            elif level == 3:
                # 1-2-3结构
                level3_data = node_data.copy()
                level3_data['children'] = []
                parent2_id = self.tree_retriever.get_parent_node(node_id)
                if parent2_id:
                    parent2_data = self.tree_retriever.get_node_data(parent2_id)
                    if parent2_data and parent2_data.get('level') == 2:
                        parent2_node = parent2_data.copy()
                        parent2_node['children'] = [level3_data]
                        parent1_id = self.tree_retriever.get_parent_node(parent2_id)
                        if parent1_id:
                            parent1_data = self.tree_retriever.get_node_data(parent1_id)
                            if parent1_data and parent1_data.get('level') == 1:
                                parent1_node = parent1_data.copy()
                                parent1_node['children'] = [parent2_node]
                                result_dict[parent1_id] = parent1_node
                        result_dict[parent2_id] = parent2_node
                result_dict[node_id] = level3_data
            # 去重，只保留最外层结构
        # 只返回level1节点为根的结构，如果没有level1则返回level2，否则level3
        roots = []
        for node in result_dict.values():
            if node.get('level') == 1:
                roots.append(node)
        if not roots:
            for node in result_dict.values():
                if node.get('level') == 2:
                    roots.append(node)
        if not roots:
            for node in result_dict.values():
                if node.get('level') == 3:
                    roots.append(node)
        return roots
    

    def _build_hierarchical_knowledge(self, knowledge: list) -> list:
        """
        构建层级化的知识结构：
        - level1节点：只查自己和直接下属的level2节点（1-2结构）
        - level2节点：查自己、父level1、所有下属level3节点（1-2-3结构）
        - level3节点：查自己、父level2、祖父level1（1-2-3结构）
        """
        from collections import OrderedDict
        result_dict = OrderedDict()  # id -> node结构



        for node_id in knowledge:
            node_data = self.tree_retriever.get_node_data(node_id)
            if not node_data:
                continue
            level = node_data.get('level')

            if level == 1:
                # 1-2结构
                node = node_data.copy()
                node['children'] = []
            
                for level2_id in self.tree_retriever.get_children_nodes(node_id):
                    level2_data = self.tree_retriever.get_node_data(level2_id)
                    if level2_data and level2_data.get('level') == 2:
                        level2_node = level2_data.copy()
                        level2_node['children'] = []
                        node['children'].append(level2_node)
                        result_dict[level2_id] = level2_node
                result_dict[node_id] = node
            
            elif level == 2:
                # 1-2-3结构
                level2_data = node_data.copy()
                level2_data['children'] = []
                # level3 children
                for level3_id in self.tree_retriever.get_children_nodes(node_id):
                    level3_data = self.tree_retriever.get_node_data(level3_id)
                    if level3_data and level3_data.get('level') == 3:
                        level3_node = level3_data.copy()
                        level3_node['children'] = []
                        level2_data['children'].append(level3_node)
                        result_dict[level3_id] = level3_node
                # 父level1
                parent1_id = self.tree_retriever.get_parent_node(node_id)
                if parent1_id:
                    parent1_data = self.tree_retriever.get_node_data(parent1_id)
                    if parent1_data and parent1_data.get('level') == 1:
                        parent1_node = parent1_data.copy()
                        parent1_node['children'] = [level2_data]
                        result_dict[parent1_id] = parent1_node
                result_dict[node_id] = level2_data

            elif level == 3:
                # 1-2-3结构
                level3_data = node_data.copy()
                level3_data['children'] = []
                parent2_id = self.tree_retriever.get_parent_node(node_id)
                if parent2_id:
                    parent2_data = self.tree_retriever.get_node_data(parent2_id)
                    if parent2_data and parent2_data.get('level') == 2:
                        parent2_node = parent2_data.copy()
                        parent2_node['children'] = [level3_data]
                        parent1_id = self.tree_retriever.get_parent_node(parent2_id)
                        if parent1_id:
                            parent1_data = self.tree_retriever.get_node_data(parent1_id)
                            if parent1_data and parent1_data.get('level') == 1:
                                parent1_node = parent1_data.copy()
                                parent1_node['children'] = [parent2_node]
                                result_dict[parent1_id] = parent1_node
                        result_dict[parent2_id] = parent2_node
                result_dict[node_id] = level3_data
            # 去重，只保留最外层结构
        # 只返回level1节点为根的结构，如果没有level1则返回level2，否则level3
        roots = []
        for node in result_dict.values():
            if node.get('level') == 1:
                roots.append(node)
        if not roots:
            for node in result_dict.values():
                if node.get('level') == 2:
                    roots.append(node)
        if not roots:
            for node in result_dict.values():
                if node.get('level') == 3:
                    roots.append(node)
        return roots
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        if not self.initialized:
            return {"error": "系统未初始化"}
        
        stats = {
            "initialized": self.initialized,
            "tree_data_loaded": len(self.tree_retriever.raw_data) > 0,
            "llm_connected": self.llm_service.test_connection()
        }
        
        return stats
    
    def test_query(self, query: str = "什么是1-Methylhistidine？") -> Dict[str, Any]:
        """测试查询功能"""
        if not self.initialized:
            self.initialize()
        
        return self.query(query)
    
    def rebuild_knowledge_base(self, batch_size: int = 1000):
        """重新构建知识库"""
        print("开始重新构建知识库...")
        self.semantic_store, self.node_store = self.knowledge_builder.build_all_stores(
            self.raw_data_path,
            self.synonyms_path, 
            self.tree_structure_path,
            batch_size=batch_size
        )
        print("知识库重建完成")
    
    def search_semantic_store(self, query: str, k: int = 5) -> List[Dict]:
        """直接搜索语义超图库（用于调试）"""
        if not self.semantic_store:
            return []
        
        results = self.semantic_store.search(query, k=k)
        return [{"text": doc.page_content, "metadata": doc.metadata} for doc in results]
    
    def search_node_store(self, query: str, k: int = 5) -> List[Dict]:
        """直接搜索节点库（用于调试）"""
        if not self.node_store:
            return []
        
        results = self.node_store.search(query, k=k)
        return [{"text": doc.page_content, "metadata": doc.metadata} for doc in results] 