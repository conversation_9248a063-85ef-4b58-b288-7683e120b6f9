from langchain_community.vectorstores import Chroma

from langchain.schema import Document

import os
import uuid
from typing import Optional

from langchain_huggingface import HuggingFaceEmbeddings

import torch
from config import Config

class VectorStoreUtils:
    def __init__(self, persist_directory: str, collection_name: str = "default", embedding=None):
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        self.embedding = embedding or  HuggingFaceEmbeddings(
            model_name= Config.get("EMBEDDING_MODEL_PATH") or  "BAAI/bge-base-zh",
            model_kwargs={"device": "cuda" if torch.cuda.is_available() else "cpu"},
            encode_kwargs={"normalize_embeddings": True}
        )
        os.makedirs(persist_directory, exist_ok=True)

        self.vectorstore = Chroma(
            collection_name=self.collection_name,
            persist_directory=self.persist_directory,
            embedding_function=self.embedding
        )

    def add_documents(self, texts: list[str], metadatas: list[dict] = None):
        """
        添加文本到向量库。每个文本可附加一个 metadata 字典。
        """
        metadatas = metadatas or [{}] * len(texts)
        ids = [str(uuid.uuid4()) for _ in texts]
        self.vectorstore.add_texts(texts=texts, metadatas=metadatas, ids=ids)
        self.vectorstore.persist()
        return ids

    def delete_document(self, doc_id: str):
        """
        删除指定 ID 的文档。
        """
        self.vectorstore.delete([doc_id])
        self.vectorstore.persist()

    def update_document(self, doc_id: str, new_text: str, new_metadata: dict = None):
        """
        更新文档：先删后增（保留原 ID）。
        """
        self.delete_document(doc_id)
        self.vectorstore.add_texts(
            texts=[new_text],
            metadatas=[new_metadata or {}],
            ids=[doc_id]
        )
        self.vectorstore.persist()

    def search(self, query: str, k: int = 4, with_score: bool = False, level_filter: Optional[int] = None):
        """
        根据问题向量化后查询相似文档。
        
        Args:
            query: 查询文本
            k: 返回结果数量
            with_score: 是否返回相似度分数
            level_filter: 按level过滤结果（可选）
        """
        filter_param = {"level": level_filter} if level_filter is not None else None

        if with_score:
            results = self.vectorstore.similarity_search_with_score(
                query,
                k=k * 5,  # 放大搜索范围以保证过滤后仍有足够结果
                filter=filter_param
            )
            # 截取前 k 个结果（因为过滤后可能仍大于 k）
            return results[:k]
        else:
            results = self.vectorstore.similarity_search(
                query,
                k=k * 5,
                filter=filter_param
            )
            return results[:k]

       

    def list_all_documents(self):
        """
        返回向量库中所有文档（注意：Chroma 本身不直接暴露全量文档接口）。
        推荐自行管理文档元数据或数据库同步。
        """
        raise NotImplementedError("Chroma doesn't natively support full document listing.")
    
    def get_stats(self):
        """
        获取向量库统计信息
        """
        try:
            collection = self.vectorstore._collection
            count = collection.count()
            return {
                'total_documents': count,
                'collection_name': self.collection_name,
                'persist_directory': self.persist_directory,
                'vector_store_type': 'chroma'
            }
        except Exception as e:
            return {
                'total_documents': 0,
                'collection_name': self.collection_name,
                'persist_directory': self.persist_directory,
                'vector_store_type': 'chroma',
                'error': str(e)
            }
    
    def force_save(self):
        """
        强制保存向量库
        """
        self.vectorstore.persist()
    
    def rebuild_index(self):
        """
        重建索引（Chroma不需要重建，但保持接口兼容）
        """
        print("Chroma向量库不需要重建索引")
        return

