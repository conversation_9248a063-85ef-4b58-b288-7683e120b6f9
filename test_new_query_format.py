#!/usr/bin/env python3
"""
测试新的查询格式功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from service.rag_service import RAGService

def test_new_query_format():
    """测试新的查询格式"""
    print("=== 测试新的查询格式 ===")
    
    # 初始化RAG服务
    rag_service = RAGService()
    
    try:
        # 初始化服务（不重建知识库）
        rag_service.initialize(rebuild_knowledge_base=False)
        
        # 测试数据：新的查询格式
        test_query_data = [
            {'id': 8228, 'query': [1, 3]}, 
            {'id': 8227, 'query': [1, 3]}, 
            {'id': 7936, 'query': [2]}
        ]
        
        print(f"测试查询数据: {test_query_data}")
        
        # 调用新的方法
        result = rag_service._build_hierarchical_knowledge_by_query(test_query_data)
        
        print(f"返回结果数量: {len(result)}")
        print("返回结果:")
        for i, node in enumerate(result):
            print(f"  节点 {i+1}:")
            print(f"    ID: {node.get('id')}")
            print(f"    Level: {node.get('level')}")
            print(f"    Name: {node.get('name', 'N/A')}")
            print(f"    Children count: {len(node.get('children', []))}")
            
            # 显示子节点信息
            for j, child in enumerate(node.get('children', [])):
                print(f"      子节点 {j+1}: ID={child.get('id')}, Level={child.get('level')}, Name={child.get('name', 'N/A')}")
        
        print("=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_with_real_query():
    """使用真实查询测试完整流程"""
    print("\n=== 测试完整查询流程 ===")
    
    rag_service = RAGService()
    
    try:
        # 初始化服务
        rag_service.initialize(rebuild_knowledge_base=False)
        
        # 测试查询
        test_question = "什么是1-Methylhistidine？"
        print(f"测试问题: {test_question}")
        
        result = rag_service.query(test_question)
        
        if result.get('success'):
            print("查询成功!")
            print(f"答案: {result.get('answer', 'N/A')[:200]}...")
            print(f"层级结构数量: {result.get('hierarchical_structure_count', 0)}")
        else:
            print(f"查询失败: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"完整流程测试中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 测试新的查询格式方法
    test_new_query_format()
    
    # 测试完整流程
    test_with_real_query()
