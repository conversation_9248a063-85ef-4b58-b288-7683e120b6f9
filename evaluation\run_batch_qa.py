"""
批量问答运行脚本
提供常用的预设配置，支持断点继续和失败文件重处理
"""

import argparse
import sys
import json
import time
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from batch_qa import BatchQAProcessor


class ResumableBatchProcessor:
    """支持断点继续的批量处理器"""

    # 定义失败类型常量
    FAILED_TYPE = -1  # 失败问题的type标记

    def __init__(self, vector_store_type: str = "chroma", max_workers: int = 4):
        self.processor = BatchQAProcessor(vector_store_type=vector_store_type, max_workers=max_workers)

    def get_processed_ids_from_results(self, output_file: str) -> set:
        """从结果文件中获取已处理的ID（包括成功和失败的）"""
        if not Path(output_file).exists():
            return set()

        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                results = json.load(f)

            processed_ids = set()
            for result in results:
                result_id = result.get("id")
                if result_id is not None:
                    processed_ids.add(result_id)

            return processed_ids
        except Exception as e:
            print(f"读取结果文件失败: {e}")
            return set()

    def get_failed_questions_from_results(self, output_file: str) -> List[Dict[str, Any]]:
        """从结果文件中获取失败的问题（type为FAILED_TYPE的）"""
        if not Path(output_file).exists():
            return []

        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                results = json.load(f)

            failed_questions = []
            for result in results:
                if result.get("type") == self.FAILED_TYPE:
                    # 将失败的结果转换回原始问题格式
                    original_question = {
                        "id": result.get("id"),
                        "type": result.get("original_type", 1),  # 恢复原始type
                        "question": result.get("question", "")
                    }
                    failed_questions.append(original_question)

            return failed_questions
        except Exception as e:
            print(f"读取失败问题失败: {e}")
            return []

    def get_failed_file(self, output_file: str) -> str:
        """获取失败文件路径"""
        output_path = Path(output_file)
        return str(output_path.parent / f"{output_path.stem}_failed.json")

    def get_statistics_from_results(self, output_file: str, total_count: int) -> Dict[str, Any]:
        """从结果文件中获取统计信息"""
        if not Path(output_file).exists():
            return {
                "processed_count": 0,
                "failed_count": 0,
                "success_count": 0,
                "total_count": total_count
            }

        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                results = json.load(f)

            processed_count = len(results)
            success_count = sum(1 for r in results if r.get("success", False))
            failed_count = processed_count - success_count

            return {
                "processed_count": processed_count,
                "failed_count": failed_count,
                "success_count": success_count,
                "total_count": total_count
            }
        except Exception as e:
            print(f"读取统计信息失败: {e}")
            return {
                "processed_count": 0,
                "failed_count": 0,
                "success_count": 0,
                "total_count": total_count
            }

    def save_failed_questions(self, failed_file: str, failed_questions: List[Dict[str, Any]]):
        """保存失败的问题"""
        with open(failed_file, 'w', encoding='utf-8') as f:
            json.dump(failed_questions, f, ensure_ascii=False, indent=2)

    def load_failed_questions(self, failed_file: str) -> List[Dict[str, Any]]:
        """加载失败的问题"""
        if not Path(failed_file).exists():
            return []

        with open(failed_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def process_questions_incremental(self, all_questions: List[Dict[str, Any]],
                                     questions_to_process: List[Dict[str, Any]],
                                     output_file: str, save_interval: int = 10):
        """
        增量处理问题，每处理一定数量就保存一次

        Args:
            all_questions: 所有问题列表（用于索引映射）
            questions_to_process: 需要处理的问题列表
            output_file: 输出文件路径
            save_interval: 保存间隔（每处理多少个问题保存一次）

        Returns:
            处理结果列表
        """
        import concurrent.futures
        from tqdm import tqdm

        # 不再需要单独的失败文件

        # 加载已有结果
        all_results = []
        if Path(output_file).exists():
            with open(output_file, 'r', encoding='utf-8') as f:
                all_results = json.load(f)

        # 如果结果数量不足，补充空结果
        while len(all_results) < len(all_questions):
            all_results.append(None)

        # 创建问题ID到索引的映射
        id_to_index = {}
        for i, question in enumerate(all_questions):
            question_id = question.get("id")
            if question_id is not None:
                id_to_index[question_id] = i

        print(f"开始增量处理 {len(questions_to_process)} 个问题，每 {save_interval} 个保存一次")

        processed_count = 0

        # 使用ThreadPoolExecutor进行并发处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.processor.max_workers) as executor:
            # 使用tqdm显示进度
            with tqdm(total=len(questions_to_process), desc="处理问题", unit="个") as pbar:

                # 分批处理
                for batch_start in range(0, len(questions_to_process), save_interval):
                    batch_end = min(batch_start + save_interval, len(questions_to_process))
                    batch_questions = questions_to_process[batch_start:batch_end]

                    # 创建当前批次的任务
                    future_to_question = {}
                    for i, question_data in enumerate(batch_questions):
                        # 轮询分配RAG服务实例
                        rag_service = self.processor.rag_services[i % len(self.processor.rag_services)]
                        future = executor.submit(self.processor.process_single_question, question_data, rag_service)
                        future_to_question[future] = (batch_start + i, question_data)

                    # 处理当前批次，改进异常处理机制
                    batch_results = []
                    timeout_seconds = 300  # 每个问题最多等待120秒
                    batch_timeout = timeout_seconds * len(batch_questions)
                    completed_futures = set()

                    try:
                        # 使用 as_completed 处理任务，包装在 try-catch 中
                        for future in concurrent.futures.as_completed(future_to_question, timeout=batch_timeout):
                            completed_futures.add(future)
                            local_index, question_data = future_to_question[future]

                            try:
                                # 获取单个任务结果
                                result = future.result(timeout=5)  # 短超时，因为任务已完成
                                batch_results.append((local_index, result))

                                # 更新进度条
                                pbar.update(1)
                                processed_count += 1

                                # 显示处理状态
                                if result["success"]:
                                    pbar.set_postfix({"状态": "成功", "已处理": processed_count})
                                else:
                                    pbar.set_postfix({"状态": "失败", "已处理": processed_count})

                            except Exception as e:
                                # 单个任务异常 - 标记为失败类型
                                error_result = {
                                    "id": question_data.get("id"),
                                    "type": self.FAILED_TYPE,  # 标记为失败
                                    "original_type": question_data.get("type"),  # 保存原始type
                                    "question": question_data.get("question", ""),
                                    "contexts": [],
                                    "answer": "",
                                    "success": False,
                                    "error": str(e),
                                    "error_details": {
                                        "error_type": "processing_exception",
                                        "error_message": str(e),
                                        "llm_response": None
                                    }
                                }
                                batch_results.append((local_index, error_result))
                                pbar.update(1)
                                processed_count += 1
                                pbar.set_postfix({"状态": "异常", "已处理": processed_count})
                                print(f"\n错误: 问题 ID {question_data.get('id')} 处理异常: {str(e)[:100]}...")

                    except concurrent.futures.TimeoutError:
                        # 批次超时处理
                        print(f"\n警告: 批次处理超时（{batch_timeout}秒），处理未完成的任务...")

                        # 取消所有未完成的任务
                        for future in future_to_question:
                            if future not in completed_futures:
                                try:
                                    future.cancel()
                                except:
                                    pass  # 忽略取消失败

                    except Exception as e:
                        # 批次级别异常
                        print(f"\n严重错误: 批次处理异常: {str(e)}")

                        # 取消所有未完成的任务
                        for future in future_to_question:
                            if future not in completed_futures:
                                try:
                                    future.cancel()
                                except:
                                    pass  # 忽略取消失败

                    # 检查是否有未完成的任务
                    completed_indices = {idx for idx, _ in batch_results}
                    for i, question_data in enumerate(batch_questions):
                        local_index = batch_start + i
                        if local_index not in completed_indices:
                            # 处理未完成的任务 - 标记为失败类型
                            error_result = {
                                "id": question_data.get("id"),
                                "type": self.FAILED_TYPE,  # 标记为失败
                                "original_type": question_data.get("type"),  # 保存原始type
                                "question": question_data.get("question", ""),
                                "contexts": [],
                                "answer": "",
                                "success": False,
                                "error": "任务未完成（超时或被取消）",
                                "error_details": {
                                    "error_type": "timeout_or_cancelled",
                                    "error_message": "任务未完成（超时或被取消）",
                                    "llm_response": None
                                }
                            }
                            batch_results.append((local_index, error_result))
                            pbar.update(1)
                            processed_count += 1
                            print(f"\n警告: 问题 ID {question_data.get('id')} 未完成处理")

                    # 将批次结果合并到总结果中
                    for local_index, result in batch_results:
                        question_data = questions_to_process[local_index]
                        question_id = question_data.get("id")

                        # 找到在总问题列表中的索引
                        if question_id is not None and question_id in id_to_index:
                            global_index = id_to_index[question_id]
                            all_results[global_index] = result

                    # 保存当前结果
                    self.save_incremental_results(all_results, output_file)

                    print(f"已处理 {processed_count}/{len(questions_to_process)} 个问题，已保存结果")

        return [r for r in all_results if r is not None]

    def save_incremental_results(self, all_results: List[Dict[str, Any]], output_file: str):
        """
        保存增量结果
        """
        # 过滤掉None结果并保存
        valid_results = [r for r in all_results if r is not None]

        # 保存结果文件
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(valid_results, f, ensure_ascii=False, indent=2)

    def run_resumable_processing(self, questions_file: str, output_file: str,
                               rebuild_knowledge_base: bool = False, batch_size: int = 1000,
                               limit: int = None, save_interval: int = 10):
        """
        运行可恢复的批量处理（自动处理失败问题）

        Args:
            questions_file: 问题文件路径
            output_file: 输出文件路径
            rebuild_knowledge_base: 是否重新构建知识库
            batch_size: 批处理大小
            limit: 限制处理的问题数量
            save_interval: 保存间隔（每处理多少个问题保存一次）
        """

        # 加载原始问题
        print(f"加载问题文件: {questions_file}")
        with open(questions_file, 'r', encoding='utf-8') as f:
            all_questions = json.load(f)

        if limit and limit > 0:
            all_questions = all_questions[:limit]
            print(f"限制处理前 {limit} 个问题")

        # 智能确定要处理的问题（合并断点继续和重试逻辑）
        processed_ids = self.get_processed_ids_from_results(output_file)
        failed_questions = self.get_failed_questions_from_results(output_file)

        if processed_ids:
            print(f"发现已处理 {len(processed_ids)} 个问题")

            if failed_questions:
                print(f"发现 {len(failed_questions)} 个失败问题，将重新处理")
                failed_ids = [q.get("id") for q in failed_questions]
                print(f"失败问题ID: {failed_ids[:10]}{'...' if len(failed_ids) > 10 else ''}")

            # 找出未处理的问题（包括失败的和从未处理的）
            unprocessed_questions = [q for q in all_questions
                                   if q.get("id") not in processed_ids]

            # 合并失败问题和未处理问题
            questions_to_process = failed_questions + unprocessed_questions

            if questions_to_process:
                print(f"总共需要处理 {len(questions_to_process)} 个问题")
                print(f"  - 重新处理失败: {len(failed_questions)} 个")
                print(f"  - 继续处理新问题: {len(unprocessed_questions)} 个")
            else:
                print("所有问题都已成功处理")
                return
        else:
            questions_to_process = all_questions
            print("没有找到已处理的结果，从头开始处理所有问题")

        if not questions_to_process:
            print("没有需要处理的问题")
            return

        # 初始化RAG服务
        self.processor.initialize_rag_services(rebuild_knowledge_base, batch_size)

        # 增量处理问题
        print(f"开始增量处理 {len(questions_to_process)} 个问题，每 {save_interval} 个保存一次")
        start_time = time.time()

        final_results = self.process_questions_incremental(
            all_questions, questions_to_process, output_file, save_interval)

        end_time = time.time()
        total_time = end_time - start_time

        # 最终统计（失败信息已经在结果文件中）
        successful_count = 0
        failed_count = 0

        for result in final_results:
            if result.get("success", False):
                successful_count += 1
            else:
                failed_count += 1

        print(f"处理完成，耗时: {total_time:.2f} 秒")
        print(f"成功: {successful_count}, 失败: {failed_count}")
        if len(final_results) > 0:
            print(f"成功率: {successful_count/len(final_results)*100:.2f}%")

        # 检查是否还有失败问题
        current_failed = self.get_failed_questions_from_results(output_file)
        if current_failed:
            print(f"\n仍有 {len(current_failed)} 个问题处理失败")
            print(f"失败问题已标记在结果文件中（type = {self.FAILED_TYPE}）")
            print(f"可以重新运行相同命令继续处理失败的问题")
        else:
            print("\n所有问题都已成功处理！")


def run_quick_test():
    """快速测试模式 - 处理前6个问题"""
    print("=== 快速测试模式 ===")
    processor = ResumableBatchProcessor(vector_store_type="chroma", max_workers=3)
    processor.run_resumable_processing(
        questions_file="evaluation/1.json",
        output_file="evaluation/quick_test_results.json",
        rebuild_knowledge_base=False,
        batch_size=1000,
        limit=6,
        save_interval=2  # 测试模式每2个问题保存一次
    )




def run_small_batch():
    """小批量模式 - 处理前100个问题"""
    print("=== 小批量模式 ===")
    processor = ResumableBatchProcessor(vector_store_type="chroma", max_workers=2)
    processor.run_resumable_processing(
        questions_file="evaluation/1.json",
        output_file="evaluation/small_batch_results.json",
        rebuild_knowledge_base=False,
        batch_size=1000,
        limit=100,
        save_interval=5  # 每5个问题保存一次
    )


def run_full_batch():
    """完整批量模式 - 处理所有问题"""
    print("=== 完整批量模式 ===")
    print("注意: 使用单线程处理以避免 ChromaDB 冲突")
    processor = ResumableBatchProcessor(vector_store_type="chroma", max_workers=1)
    processor.run_resumable_processing(
        questions_file="eva_data/type_outputs/3.json",
        output_file="evaluation/ragas/eva_data/standard/cache/3_results.json",
        rebuild_knowledge_base=False,
        batch_size=1000,
        limit=None,
        save_interval=5   # 减少保存间隔，更频繁保存
    )


def process_file(file_path: str):
    """处理指定的文件（自动处理失败问题和断点继续）"""
    print(f"=== 处理文件: {file_path} ===")

    if not Path(file_path).exists():
        print(f"错误: 文件 {file_path} 不存在")
        return

    # 从文件路径推断问题文件
    questions_file = "eva_data/type_outputs/2.json"  # 默认问题文件

    processor = ResumableBatchProcessor(vector_store_type="chroma", max_workers=1)
    processor.run_resumable_processing(
        questions_file=questions_file,
        output_file=file_path,
        rebuild_knowledge_base=False,
        batch_size=1000,
        limit=None,
        save_interval=5
    )



def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="智能批量问答处理脚本，自动处理失败问题和断点继续")
    parser.add_argument("mode", choices=["test", "small", "full", "process"],
                       help="运行模式: test(6个问题), small(100个问题), full(全部问题), process(处理指定文件)")
    parser.add_argument("--file", type=str,
                       help="要处理的文件路径（process模式必需）")

    args = parser.parse_args()

    try:
        if args.mode == "test":
            run_quick_test()
        elif args.mode == "small":
            run_small_batch()
        elif args.mode == "full":
            run_full_batch()
        elif args.mode == "process":
            if not args.file:
                print("错误: process模式需要指定 --file 参数")
                return
            process_file(args.file)

        print(f"\n=== {args.mode} 模式完成 ===")

    except Exception as e:
        print(f"运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("使用方法:")
        print("  python evaluation/run_batch_qa.py test                      # 快速测试(6个问题)")
        print("  python evaluation/run_batch_qa.py small                     # 小批量(100个问题)")
        print("  python evaluation/run_batch_qa.py full                      # 完整批量(所有问题)")
        print("  python evaluation/run_batch_qa.py process --file <path>     # 处理指定文件")
        print("\n选项:")
        print("  --file <path>   指定要处理的文件路径（process模式必需）")
        print("\n功能特性:")
        print("  ✓ 智能处理: 自动识别失败问题并重新处理")
        print("  ✓ 断点继续: 基于ID自动从上次位置继续处理")
        print("  ✓ 失败标记: 失败问题直接标记在结果文件中（type=-1）")
        print("  ✓ 增量保存: 实时保存结果，避免中断损失")
        print("  ✓ 并发处理: 多线程处理，提高处理效率")
        print("\n建议流程:")
        print("  1. 首次使用先运行 test 模式验证功能")
        print("  2. 确认无误后使用 small 模式测试性能")
        print("  3. 最后使用 full 模式处理所有问题")
        print("  4. 如果有失败问题，重新运行相同命令自动重试")
        print("  5. 程序会自动增量保存，中断后可断点继续")
        print("\n示例:")
        print("  python evaluation/run_batch_qa.py test")
        print("  python evaluation/run_batch_qa.py full")
        print("  python evaluation/run_batch_qa.py process --file results.json")
        print("\n调试环境:")
        print("  conda activate ms-py_312")
    else:
        main()
