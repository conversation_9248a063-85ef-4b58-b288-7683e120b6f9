"""
    提取json文件中指定字段的代谢物数据
"""

import json
import os
try:
    import ijson
    IJSON_AVAILABLE = True
except ImportError:
    IJSON_AVAILABLE = False
    print("警告：ijson库不可用，将尝试使用标准json库（可能导致内存问题）")

def extract_metabolites_data():
    """
    hmdb_metabolites.json文件中提取指定字段的数据
    """
    # 输入文件路径
    input_path = "./processed_data/hmdb_metabolites.json"
    
    # 检查文件是否存在
    if not os.path.exists(input_path):
        print(f"错误：文件 {input_path} 不存在")
        return
    
    # 使用流式解析方法
    if IJSON_AVAILABLE:
        return _extract_with_streaming(input_path)
    else:
        return _extract_with_standard_json(input_path)

def _extract_with_streaming(input_path):
    """
    使用ijson进行流式解析，适用于大型JSON文件
    """
    print("正在使用流式解析方法读取数据...")
    
    extracted_data = []
    metabolite_count = 0
    
    try:
        with open(input_path, 'rb') as f:
            # 使用ijson流式解析JSON文件中的metabolite数组
            parser = ijson.items(f, 'hmdb.metabolite.item')
            
            print("开始处理代谢物数据...")
            
            for metabolite in parser:
                metabolite_count += 1
                
                # 显示进度
                if metabolite_count % 1000 == 0:
                    print(f"已处理 {metabolite_count} 个代谢物...")
                
                # 提取基本信息
                metabolite_info = {
                    'accession': metabolite.get('accession', ''),
                    'name': metabolite.get('name', ''),
                    'description': metabolite.get('description', ''),
                }
                
                # 提取同义词数据
                if 'synonyms' in metabolite and metabolite['synonyms']:
                    synonyms = metabolite['synonyms'].get('synonym', [])
                    if isinstance(synonyms, list):
                        metabolite_info['synonyms'] = synonyms
                    else:
                        metabolite_info['synonyms'] = [synonyms] if synonyms else []
                else:
                    metabolite_info['synonyms'] = []
                
                # 提取正常浓度数据
                if 'normal_concentrations' in metabolite and metabolite['normal_concentrations']:
                    concentrations = metabolite['normal_concentrations'].get('concentration', [])
                    if isinstance(concentrations, list):
                        metabolite_info['normal_concentrations'] = concentrations
                    else:
                        metabolite_info['normal_concentrations'] = [concentrations] if concentrations else []
                else:
                    metabolite_info['normal_concentrations'] = []
                
                # 提取异常浓度数据
                if 'abnormal_concentrations' in metabolite and metabolite['abnormal_concentrations']:
                    concentrations = metabolite['abnormal_concentrations'].get('concentration', [])
                    if isinstance(concentrations, list):
                        metabolite_info['abnormal_concentrations'] = concentrations
                    else:
                        metabolite_info['abnormal_concentrations'] = [concentrations] if concentrations else []
                else:
                    metabolite_info['abnormal_concentrations'] = []
                
                # 提取疾病数据
                if 'diseases' in metabolite and metabolite['diseases']:
                    diseases = metabolite['diseases'].get('disease', [])
                    if isinstance(diseases, list):
                        metabolite_info['diseases'] = diseases
                    else:
                        metabolite_info['diseases'] = [diseases] if diseases else []
                else:
                    metabolite_info['diseases'] = []
                
                extracted_data.append(metabolite_info)
                
    except Exception as e:
        print(f"流式解析过程中出现错误: {e}")
        return None
    
    print(f"流式解析完成！共处理了 {len(extracted_data)} 个代谢物")
    return _save_and_report(extracted_data)

def _extract_with_standard_json(input_path):
    """
    使用标准json库的回退方法（可能导致内存问题）
    """
    print("正在使用标准JSON解析方法读取数据...")
    print("警告：大文件可能导致内存不足！")
    
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except MemoryError:
        print("错误：文件过大，内存不足！请安装ijson库：pip install ijson")
        return None
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None
    
    # 获取代谢物数据
    metabolite_data = data['hmdb']['metabolite']
    
    # 提取需要的字段
    extracted_data = []
    
    print(f"正在处理 {len(metabolite_data)} 个代谢物...")
    
    for metabolite in metabolite_data:
        # 提取基本信息
        metabolite_info = {
            'accession': metabolite.get('accession', ''),
            'name': metabolite.get('name', ''),
            'description': metabolite.get('description', ''),
            'synonyms' : metabolite['synonyms']['synonym']
        }
        
        # 提取正常浓度数据
        if 'normal_concentrations' in metabolite and metabolite['normal_concentrations']:
            metabolite_info['normal_concentrations'] = metabolite['normal_concentrations']['concentration']
        else:
            metabolite_info['normal_concentrations'] = []
        
        # 提取异常浓度数据
        if 'abnormal_concentrations' in metabolite and metabolite['abnormal_concentrations']:
            metabolite_info['abnormal_concentrations'] = metabolite['abnormal_concentrations']['concentration']
        else:
            metabolite_info['abnormal_concentrations'] = []
        
        # 提取疾病数据
        if 'diseases' in metabolite and metabolite['diseases']:
            metabolite_info['diseases'] = metabolite['diseases']['disease']
        else:
            metabolite_info['diseases'] = []
        
        extracted_data.append(metabolite_info)
    
    return _save_and_report(extracted_data)

def _save_and_report(extracted_data):
    """
    保存提取的数据并生成报告
    """
    if not extracted_data:
        print("没有数据需要保存")
        return None
        
    # 保存提取的数据
    output_path = "./processed_data/hmdb_metabolites_processed.json"
    
    # 创建输出目录（如果不存在）
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 获取绝对路径
    abs_output_path = os.path.abspath(output_path)
    print(f"正在保存数据到 {abs_output_path}...")
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(extracted_data, f, ensure_ascii=False, indent=2)
    
    # 验证文件是否创建成功
    if os.path.exists(output_path):
        file_size = os.path.getsize(output_path)
        print(f"文件成功创建，大小: {file_size} bytes")
    else:
        print("警告：文件创建失败！")
    
    print(f"数据提取完成！共处理了 {len(extracted_data)} 个代谢物")
    print(f"结果已保存到: {output_path}")
    
    # 显示一些统计信息
    total_normal = sum(len(item['normal_concentrations']) for item in extracted_data)
    total_abnormal = sum(len(item['abnormal_concentrations']) for item in extracted_data)
    total_diseases = sum(len(item['diseases']) for item in extracted_data)
    
    print(f"\n统计信息:")
    print(f"- 总代谢物数量: {len(extracted_data)}")
    print(f"- 正常浓度记录数: {total_normal}")
    print(f"- 异常浓度记录数: {total_abnormal}")
    print(f"- 疾病记录数: {total_diseases}")
    
    return extracted_data



if __name__ == "__main__":
    # 执行数据提取
    extracted_data = extract_metabolites_data()
    