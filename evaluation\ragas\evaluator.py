from typing import List, Dict, Any
from datasets import Dataset
from ragas import evaluate
from ragas.llms import LangchainLLMWrapper
from ragas.metrics import LLMContextRecall, Faithfulness, FactualCorrectness, AnswerRelevancy, ContextPrecision, ContextRelevance, SemanticSimilarity
from langchain_openai import ChatOpenAI
import os
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.language_models import BaseLanguageModel as LangchainLLM
from langchain.embeddings import HuggingFaceEmbeddings
def prepare_evaluation_dataset(questions: List[str], ground_truth_answers: List[str], 
                              generated_answers: List[str], retrieved_contexts: List[List[str]]) -> Dataset:
    """
    准备用于评估的数据集
    
    Args:
        questions: 问题列表
        ground_truth_answers: 标准答案列表
        generated_answers: 生成的答案列表
        retrieved_contexts: 检索到的上下文列表
        
    Returns:
        用于Ragas评估的Dataset对象
    """
    # 构造评估用的数据集
    evaluation_dataset = Dataset.from_dict({
        "question": questions,
        # "ground_truths": [[answer] for answer in ground_truth_answers],  # List[List[str]]
        "reference": ground_truth_answers,                               # List[str]
        "answer": generated_answers,                                     # List[str]
        "contexts": retrieved_contexts                                   # List[List[str]]
    })
    
    return evaluation_dataset

def evaluate_rag_results(evaluation_dataset: Dataset, api_key: str, model: str, base_url: str) -> Dict[str, Any]:
    """
    评估RAG系统生成的结果
    
    Args:
        evaluation_dataset: 评估数据集
        api_key: LLM API密钥
        model: LLM模型名称
        base_url: LLM API基础URL
        
    Returns:
        评估结果字典
    """

    # 初始化LLM
    llm = ChatOpenAI(
            base_url=base_url,
            api_key=api_key,
            model=model
        )
    

    # 检查是否有可用的CUDA设备
    import torch
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"使用设备: {device}")

    embeddings = HuggingFaceEmbeddings(
        model_name="E:/code/pythonProject/models/BAAI/bge-m3",  # 或本地路径，如 "./my_model"
        model_kwargs={"device": device},     # 自动选择设备
        encode_kwargs={"normalize_embeddings": True}
    )

    evaluator_llm = llm

    # 执行评估
    metrics = [
        LLMContextRecall(),  # ✅ 回答是否覆盖了检索到的关键信息（评估是否充分利用了上下文）
        
        Faithfulness(),      # ✅ 回答是否忠实于上下文（避免“幻觉”或编造内容）
        
        FactualCorrectness(),# ✅ 回答是否真实、准确、符合客观事实（与常识或科学一致）
        
        AnswerRelevancy(),   # ✅ 回答是否真正解答了用户的问题（是否答题，不是绕圈子）
        
        # ContextPrecision(),  # ✅ 检索到的上下文中，有多少内容对回答有帮助（检索精度）
        
        # ContextRelevance(),  # ✅ 检索内容是否与用户问题相关（评估 Retriever 的召回质量）
        
        # SemanticSimilarity(), # ✅ 回答与参考答案在语义上是否一致（需提供 ground_truths）
    ]


    result = evaluate(
        dataset=evaluation_dataset,
        metrics=metrics,
        llm=evaluator_llm,
        embeddings=embeddings
    )

    
    
    return result 