# 实现批量问答功能

数据 evauation/questions.json

数据结构如下：
```json
[{
    "type": 1,
    "question": "What metabolites are associated with  Lymphosarcomatosis?",
    "answer": "AICA-riboside",
    "disease_id": 98353,
    "disease_name": "Lymphosarcomatosis",
    "metabolite_id": 98352,
    "metabolite_name": "AICA-riboside"
  }]
```
rag_qa_system.py 是调用问答的代码，我需要写个并发问答的功能，请在evaluation文件夹下实现，批处理question.json文件，最终保存的格式如下，其中contexts就是hierarchical_knowledge_json。
```
[
  {
    "question": "What safety risks can be caused by flight control system failures?",
    "contexts": [
      "ACCIDENT JUST WAITING TO HAPPEN -> CAUSAL CONTRIBUTOR -> SAFETY HAZARDS POSED BY LACK OF CONTROL",
      "ACCIDENT JUST WAITING TO HAPPEN -> CAUSAL CONTRIBUTOR -> LOW EXPERIENCE LEVEL -> CAUSAL CONTRIBUTOR -> SAFETY OF FLIGHT COMPROMISED",
      "ACCIDENT JUST WAITING TO HAPPEN -> CAUSAL CONTRIBUTOR -> THIS PRODUCES A SAFETY THREAT TO THE FLYING PUBLIC"
    ],
    "answer": "The safety risks caused by flight control system failures include compromised aircraft maneuverability, increased likelihood of accidents due to inadequate response from the flight crew, especially if they are inexperienced, and overall threats to the safety of both passengers and the general public.",
    "ground_truth": "The safety risks caused by flight control system failures include loss of control of the aircraft, compromised flight safety, and a significant threat to the safety of passengers and the public. Such failures can lead to situations where the aircraft's maneuverability is severely limited, increasing the likelihood of accidents."
  }
]
```


如果你要调试或运行代码 先激活环境 conda activate ms-py_312
