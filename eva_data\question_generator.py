import json
import random
import os
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
from config import QUESTION_COUNTS, DATA_DIR, OUTPUT_FILE, RANDOM_SEED, QUESTION_TYPE_DESCRIPTIONS

class QuestionGenerator:
    def __init__(self, data_dir: str = DATA_DIR):
        """
        初始化问题生成器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        self.tree_structure = []
        self.raw_data = {}
        self.synonyms_mapping = {}
        self.disease_metabolite_mapping = defaultdict(list)
        self.metabolite_concentration_mapping = defaultdict(list)
        self.normal_concentrations = []
        self.abnormal_concentrations = []
        
        # 设置随机种子
        random.seed(RANDOM_SEED)
        
    def load_data(self):
        """加载所有数据文件"""
        print("Loading tree structure...")
        with open(os.path.join(self.data_dir, "tree_structure.json"), 'r', encoding='utf-8') as f:
            self.tree_structure = json.load(f)
            
        print("Loading raw data...")
        with open(os.path.join(self.data_dir, "raw_data.json"), 'r', encoding='utf-8') as f:
            raw_data_list = json.load(f)
            for item in raw_data_list:
                self.raw_data[item['id']] = item
                
        print("Loading synonyms mapping...")
        with open(os.path.join(self.data_dir, "synonyms_mapping.json"), 'r', encoding='utf-8') as f:
            synonyms_list = json.load(f)
            for item in synonyms_list:
                self.synonyms_mapping[item['id']] = item['synonyms']
                
        print("Building mappings...")
        self._build_mappings()
        
    def _build_mappings(self):
        """构建疾病-代谢物-浓度的映射关系"""
        for disease_node in self.tree_structure:
            disease_id = disease_node['id']
            disease_name = self.raw_data.get(disease_id, {}).get('name', f'Disease_{disease_id}')
            
            for metabolite_node in disease_node.get('child', []):
                metabolite_id = metabolite_node['id']
                metabolite_name = self.raw_data.get(metabolite_id, {}).get('name', f'Metabolite_{metabolite_id}')
                
                # 记录疾病-代谢物关系
                self.disease_metabolite_mapping[disease_id].append({
                    'id': metabolite_id,
                    'name': metabolite_name,
                    'disease_id': disease_id,
                    'disease_name': disease_name
                })
                
                # 处理浓度信息
                for concentration_node in metabolite_node.get('child', []):
                    concentration_id = concentration_node['id']
                    concentration_data = self.raw_data.get(concentration_id, {})
                    
                    if concentration_data:
                        concentration_info = {
                            'id': concentration_id,
                            'type': concentration_data.get('type'),
                            'biospecimen': concentration_data.get('biospecimen'),
                            'concentration_value': concentration_data.get('concentration_value'),
                            'concentration_units': concentration_data.get('concentration_units'),
                            'metabolite_id': metabolite_id,
                            'metabolite_name': metabolite_name,
                            'disease_id': disease_id,
                            'disease_name': disease_name,
                            'subject_age': concentration_data.get('subject_age'),
                            'subject_sex': concentration_data.get('subject_sex'),
                            'subject_condition':concentration_data.get('subject_condition'),
                            "patient_age": concentration_data.get('patient_age'),
                            "patient_sex": concentration_data.get('patient_sex'),
                            "patient_information":  concentration_data.get('patient_information')
                        }


                        
                        self.metabolite_concentration_mapping[metabolite_id].append(concentration_info)
                        
                        # 分类正常和异常浓度
                        if concentration_data.get('type') == 'normal':
                            self.normal_concentrations.append(concentration_info)
                        elif concentration_data.get('type') == 'abnormal':
                            self.abnormal_concentrations.append(concentration_info)
    
    def get_random_synonym(self, metabolite_id: int) -> str:
        """获取代谢物的随机同义词"""
        synonyms = self.synonyms_mapping.get(metabolite_id, [])
        if synonyms:
            return random.choice(synonyms)
        return self.raw_data.get(metabolite_id, {}).get('name', f'Metabolite_{metabolite_id}')
    
    def generate_question_1(self, count: int) -> List[Dict]:
        """生成问题类型1：某个疾病的标志性代谢物是什么"""
        questions = []
        diseases = list(self.disease_metabolite_mapping.keys())
        
        if not diseases:
            print("Warning: No diseases available for question type 1")
            return questions
        
        generated_count = 0
        attempts = 0
        max_attempts = count * 10  # 防止无限循环
        
        while generated_count < count and attempts < max_attempts:
            disease_id = random.choice(diseases)
            disease_name = self.raw_data.get(disease_id, {}).get('name', f'Disease_{disease_id}')
            metabolites = self.disease_metabolite_mapping[disease_id]
            
            if metabolites:
                metabolite = random.choice(metabolites)
                question = f"What metabolites are associated with  {disease_name}?"
                answer = metabolite['name']
                
                questions.append({
                    'type': 1,
                    'question': question,
                    'answer': answer,
                    'disease_id': disease_id,
                    'disease_name': disease_name,
                    'metabolite_id': metabolite['id'],
                    'metabolite_name': metabolite['name']
                })
                generated_count += 1
            
            attempts += 1
        
        if generated_count < count:
            print(f"Warning: Only generated {generated_count} questions for type 1 (requested {count})")
        
        return questions
    
    def generate_question_2(self, count: int) -> List[Dict]:
        """生成问题类型2：xx代谢物xx浓度可能患有哪种疾病（异常浓度）"""
        questions = []
        
        if not self.abnormal_concentrations:
            print("Warning: No abnormal concentrations available for question type 2")
            return questions
        
        generated_count = 0
        attempts = 0
        max_attempts = count * 10  # 防止无限循环
        
        while generated_count < count and attempts < max_attempts:
            concentration = random.choice(self.abnormal_concentrations)
            metabolite_name = concentration['metabolite_name']
            disease_name = concentration['disease_name']
            biospecimen = concentration['biospecimen']
            value = concentration['concentration_value']
            units = concentration['concentration_units']
            patient_age = concentration['patient_age']
            patient_sex = concentration['patient_sex']
            patient_information = concentration['patient_information']

            # 构建患者条件文本
            condition_parts = []
            if patient_sex:
                condition_parts.append(f"{patient_sex}")
            if patient_age:
                condition_parts.append(f"{patient_age}")
            # if patient_information:
            #     condition_parts.append(patient_information)

            condition_text = ", ".join(condition_parts) if condition_parts else "a patient"

            # 构建问题
            if value and units:
                question = (
                    f"In {condition_text}, what disease might be indicated by "
                    f"{metabolite_name} concentration of {value} {units} in {biospecimen}?"
                )
            else:
                question = (
                    f"In {condition_text}, what disease might be indicated by "
                    f"abnormal {metabolite_name} concentration in {biospecimen}?"
                )

             
            answer = disease_name
            
            questions.append({
                'type': 2,
                'question': question,
                'answer': answer,
                'metabolite_id': concentration['metabolite_id'],
                'metabolite_name': metabolite_name,
                'disease_id': concentration['disease_id'],
                'disease_name': disease_name,
                'concentration_type': 'abnormal',
                'biospecimen': biospecimen,
                'patient_age': patient_age,
                'patient_sex': patient_sex,
                'patient_information': patient_information
            })
            generated_count += 1
            attempts += 1
        
        if generated_count < count:
            print(f"Warning: Only generated {generated_count} questions for type 2 (requested {count})")
        
        return questions
    
    def generate_question_3(self, count: int) -> List[Dict]:
        """生成问题类型3：xx代谢物xx浓度，怀疑患有某种疾病还可以补充化验哪些化合物（异常浓度）"""
        questions = []
        
        if not self.abnormal_concentrations:
            print("Warning: No abnormal concentrations available for question type 3")
            return questions
        
        generated_count = 0
        attempts = 0
        max_attempts = count * 10  # 防止无限循环
        
        while generated_count < count and attempts < max_attempts:
            concentration = random.choice(self.abnormal_concentrations)
            metabolite_name = concentration['metabolite_name']
            disease_name = concentration['disease_name']
            biospecimen = concentration['biospecimen']

            # 获取同一疾病的其他代谢物作为补充化验项目
            other_metabolites = []
            for other_conc in self.abnormal_concentrations:
                if (other_conc['disease_id'] == concentration['disease_id'] and 
                    other_conc['metabolite_id'] != concentration['metabolite_id']):
                    other_metabolites.append(other_conc['metabolite_name'])
            
            if other_metabolites:
                question = f"Given abnormal {metabolite_name} concentration in {biospecimen}, what additional compounds should be tested when suspecting {disease_name}?"
                
                questions.append({
                    'type': 3,
                    'question': question,
                    'metabolite_id': concentration['metabolite_id'],
                    'metabolite_name': metabolite_name,
                    'disease_id': concentration['disease_id'],
                    'disease_name': disease_name,
                    'concentration_type': 'abnormal',
                    'biospecimen': biospecimen,
                })
                generated_count += 1
            
            attempts += 1
        
        if generated_count < count:
            print(f"Warning: Only generated {generated_count} questions for type 3 (requested {count})")
        
        return questions
    
    def generate_question_4(self, count: int) -> List[Dict]:
        """生成问题类型4：某个代谢物的正常浓度和异常浓度是什么"""
        questions = []
        metabolites = list(self.metabolite_concentration_mapping.keys())
        
        if not metabolites:
            print("Warning: No metabolites available for question type 4")
            return questions
        
        generated_count = 0
        attempts = 0
        max_attempts = count * 100  # 防止无限循环
        
        while generated_count < count and attempts < max_attempts:
            metabolite_id = random.choice(metabolites)
            metabolite_name = self.raw_data.get(metabolite_id, {}).get('name', f'Metabolite_{metabolite_id}')
            concentrations = self.metabolite_concentration_mapping[metabolite_id]
            
            normal_conc = None
            abnormal_conc = None
            
            for conc in concentrations:
                if conc['type'] == 'normal':
                    normal_conc = conc
                elif conc['type'] == 'abnormal':
                    abnormal_conc = conc
            
            if normal_conc and abnormal_conc:
                question = f"What are the normal and abnormal concentration ranges for {metabolite_name}?"
      
                questions.append({
                    'type': 4,
                    'question': question,
                    'metabolite_id': metabolite_id,
                    'metabolite_name': metabolite_name,
                })
                generated_count += 1
            
            attempts += 1
        
        if generated_count < count:
            print(f"Warning: Only generated {generated_count} questions for type 4 (requested {count})")
        
        return questions
    
    def generate_question_5(self, count: int) -> List[Dict]:
        """生成问题类型5：xx代谢物xx浓度可能患有哪种疾病（异常浓度）【代谢物名称使用他的同义词】"""
        questions = []
        
        if not self.abnormal_concentrations:
            print("Warning: No abnormal concentrations available for question type 5")
            return questions
        
        generated_count = 0
        attempts = 0
        max_attempts = count * 10  # 防止无限循环
        
        while generated_count < count and attempts < max_attempts:
            concentration = random.choice(self.abnormal_concentrations)
            metabolite_id = concentration['metabolite_id']
            metabolite_synonym = self.get_random_synonym(metabolite_id)
            disease_name = concentration['disease_name']
            biospecimen = concentration['biospecimen']
            value = concentration['concentration_value']
            units = concentration['concentration_units']
            patient_age = concentration['patient_age']
            patient_sex = concentration['patient_sex']
            patient_information = concentration['patient_information']

            # 拼接条件上下文
            condition_parts = []
            if patient_sex:
                condition_parts.append(patient_sex)
            if patient_age:
                condition_parts.append(patient_age)
            # if patient_information:
            #     condition_parts.append(patient_information)
            condition_text = ", ".join(condition_parts) if condition_parts else "a patient"

            # 构造问题
            if value and units:
                question = (
                    f"In {condition_text}, what disease might be indicated by "
                    f"{metabolite_synonym} concentration of {value} {units} in {biospecimen}?"
                )
            else:
                question = (
                    f"In {condition_text}, what disease might be indicated by "
                    f"abnormal {metabolite_synonym} concentration in {biospecimen}?"
                )

            questions.append({
                'type': 5,
                'question': question,
                'metabolite_id': metabolite_id,
                'metabolite_name': concentration['metabolite_name'],
                'metabolite_synonym': metabolite_synonym,
                'disease_id': concentration['disease_id'],
                'disease_name': disease_name,
                'concentration_type': 'abnormal',
                'biospecimen': biospecimen,
                'patient_age': patient_age,
                'patient_sex': patient_sex,
                'patient_information': patient_information
            })
            generated_count += 1
            attempts += 1
        
        if generated_count < count:
            print(f"Warning: Only generated {generated_count} questions for type 5 (requested {count})")
        
        return questions
    
    def generate_question_6(self, count: int) -> List[Dict]:
        """生成问题类型6：xx代谢物xx浓度，怀疑患有某种疾病还可以补充化验哪些化合物（异常浓度）【代谢物名称使用他的同义词】"""
        questions = []
        
        if not self.abnormal_concentrations:
            print("Warning: No abnormal concentrations available for question type 6")
            return questions
        
        generated_count = 0
        attempts = 0
        max_attempts = count * 10  # 防止无限循环
        
        while generated_count < count and attempts < max_attempts:
            concentration = random.choice(self.abnormal_concentrations)
            metabolite_id = concentration['metabolite_id']
            metabolite_synonym = self.get_random_synonym(metabolite_id)
            disease_name = concentration['disease_name']
            biospecimen = concentration['biospecimen']
            
            # 获取同一疾病的其他代谢物作为补充化验项目
            other_metabolites = []
            for other_conc in self.abnormal_concentrations:
                if (other_conc['disease_id'] == concentration['disease_id'] and 
                    other_conc['metabolite_id'] != concentration['metabolite_id']):
                    other_metabolites.append(other_conc['metabolite_name'])
            
            if other_metabolites:
                question = f"Given abnormal {metabolite_synonym} concentration in {biospecimen}, what additional compounds should be tested when suspecting {disease_name}?"
                
                questions.append({
                    'type': 6,
                    'question': question,
                    'metabolite_id': metabolite_id,
                    'metabolite_name': concentration['metabolite_name'],
                    'metabolite_synonym': metabolite_synonym,
                    'disease_id': concentration['disease_id'],
                    'disease_name': disease_name,
                    'concentration_type': 'abnormal',
                    'biospecimen': biospecimen,
                })
                generated_count += 1
            
            attempts += 1
        
        if generated_count < count:
            print(f"Warning: Only generated {generated_count} questions for type 6 (requested {count})")
        
        return questions
    
    def generate_question_7(self, count: int) -> List[Dict]:
        """生成问题类型7：某个代谢物的正常浓度和异常浓度是什么【代谢物名称使用他的同义词】"""
        questions = []
        metabolites = list(self.metabolite_concentration_mapping.keys())
        
        if not metabolites:
            print("Warning: No metabolites available for question type 7")
            return questions
        
        generated_count = 0
        attempts = 0
        max_attempts = count * 100  # 防止无限循环
        
        while generated_count < count and attempts < max_attempts:
            metabolite_id = random.choice(metabolites)
            metabolite_name = self.raw_data.get(metabolite_id, {}).get('name', f'Metabolite_{metabolite_id}')
            metabolite_synonym = self.get_random_synonym(metabolite_id)
            concentrations = self.metabolite_concentration_mapping[metabolite_id]
            
            normal_conc = None
            abnormal_conc = None
            
            for conc in concentrations:
                if conc['type'] == 'normal':
                    normal_conc = conc
                elif conc['type'] == 'abnormal':
                    abnormal_conc = conc
            
            if normal_conc and abnormal_conc:
                question = f"What are the normal and abnormal concentration ranges for {metabolite_synonym}?"
                
                questions.append({
                    'type': 7,
                    'question': question,
                    'metabolite_id': metabolite_id,
                    'metabolite_name': metabolite_name,
                    'metabolite_synonym': metabolite_synonym,
                })
                generated_count += 1
            
            attempts += 1
        
        if generated_count < count:
            print(f"Warning: Only generated {generated_count} questions for type 7 (requested {count})")
        
        return questions
    
    def generate_all_questions(self, counts: Dict[int, int]) -> List[Dict]:
        """生成所有类型的问题"""
        all_questions = []
        
        question_generators = {
            1: self.generate_question_1,
            2: self.generate_question_2,
            3: self.generate_question_3,
            4: self.generate_question_4,
            5: self.generate_question_5,
            6: self.generate_question_6,
            7: self.generate_question_7,
           
        }
        
        for question_type, count in counts.items():
            if count > 0 and question_type in question_generators:
                print(f"Generating {count} questions of type {question_type}...")
                questions = question_generators[question_type](count)
                all_questions.extend(questions)
        
        return all_questions
    
    def save_questions(self, questions: List[Dict], output_file: str):
        """保存问题到文件"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(questions, f, ensure_ascii=False, indent=2)
        print(f"Saved {len(questions)} questions to {output_file}")

def main():
    """主函数"""
    # 创建问题生成器
    generator = QuestionGenerator()
    
    # 加载数据
    generator.load_data()
    
    # 生成问题
    print("Generating questions...")
    questions = generator.generate_all_questions(QUESTION_COUNTS)
    
    # 保存问题
    generator.save_questions(questions, OUTPUT_FILE)
    
    # 统计信息
    type_counts = {}
    for q in questions:
        q_type = q['type']
        type_counts[q_type] = type_counts.get(q_type, 0) + 1
    
    print("\nQuestion generation completed!")
    print("Question type distribution:")
    for q_type, count in sorted(type_counts.items()):
        description = QUESTION_TYPE_DESCRIPTIONS.get(q_type, f"Type {q_type}")
        print(f"Type {q_type} ({description}): {count} questions")

if __name__ == "__main__":
    main() 