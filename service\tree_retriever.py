"""
树检索服务 - 基于树结构的层级检索逻辑
"""

import json
from typing import List, Dict, Any, Set, Optional


class TreeRetriever:
    """树结构检索器"""
    
    def __init__(self):
        """初始化树检索器"""
        self.tree_structure = []
        self.raw_data = []
        self.id_to_data = {}  # ID到原始数据的映射
        self.id_to_node = {}  # ID到树节点的映射
        self.child_to_parent = {}  # 子节点到父节点的映射
        
    def load_data(self, tree_structure_path: str, raw_data_path: str):
        """加载树结构和原始数据"""
        print("正在加载树结构数据...")
        
        # 加载树结构
        with open(tree_structure_path, 'r', encoding='utf-8') as f:
            self.tree_structure = json.load(f)
        
        # 加载原始数据
        with open(raw_data_path, 'r', encoding='utf-8') as f:
            self.raw_data = json.load(f)
        
        # 构建映射
        self._build_mappings()
        print("树结构数据加载完成")
    
    def _build_mappings(self):
        """构建ID映射关系"""
        # 构建ID到原始数据的映射
        for item in self.raw_data:
            self.id_to_data[item['id']] = item
        
        # 构建树节点映射和父子关系
        def build_node_mapping(nodes, parent_id=None):
            for node in nodes:
                node_id = node['id']
                self.id_to_node[node_id] = node
                
                if parent_id is not None:
                    self.child_to_parent[node_id] = parent_id
                
                # 递归处理子节点
                if 'child' in node and node['child']:
                    build_node_mapping(node['child'], node_id)
        
        build_node_mapping(self.tree_structure)
    
    def get_node_data(self, node_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取节点的原始数据"""
        return self.id_to_data.get(node_id)
    
    def get_tree_node(self, node_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取树节点信息"""
        return self.id_to_node.get(node_id)
    
    def get_parent_node(self, node_id: int) -> Optional[int]:
        """获取父节点ID"""
        return self.child_to_parent.get(node_id)
    
    def get_children_nodes(self, node_id: int) -> List[int]:
        """获取子节点ID列表"""
        tree_node = self.get_tree_node(node_id)
        if tree_node and 'child' in tree_node:
            return [child['id'] for child in tree_node['child']]
        return []
    
    def retrieve_related_nodes(self, node_ids: List[int]) -> Dict[str, List[Dict[str, Any]]]:
        """
        根据检索到的节点ID列表，获取相关的树结构节点
        
        检索逻辑：
        - level 1节点：检索所有level 2子节点
        - level 2节点：检索level 1父节点
        - level 3节点：检索level 1和level 2节点
        
        Args:
            node_ids: 检索到的节点ID列表
            
        Returns:
            包含相关节点数据的字典
        """
        result_nodes = []
        processed_ids = set()

   
       
        
        for node_id in node_ids:
            if node_id in processed_ids:
                continue
                
            node_data = self.get_node_data(node_id)
            tree_node = self.get_tree_node(node_id)
            
            if not node_data or not tree_node:
                continue
            
            level = node_data.get('level')
        

            if level == 1:
                # Level 1节点：添加自身，然后获取所有level 2子节点
                if node_id not in processed_ids:
                    result_nodes.append(node_data)
                    processed_ids.add(node_id)
                
                # 获取level 2子节点
                children_ids = self.get_children_nodes(node_id)
                for child_id in children_ids:
                    if child_id not in processed_ids:
                        child_data = self.get_node_data(child_id)
                        if child_data and child_data.get('level') == 2:
                            result_nodes.append(child_data)
                            processed_ids.add(child_id)
            
            elif level == 2:
                # Level 2节点：添加自身，然后获取level 1父节点和level 3子节点
                if node_id not in processed_ids:
                    result_nodes.append(node_data)
                    processed_ids.add(node_id)
                
                # 获取level 1父节点
                parent_id = self.get_parent_node(node_id)
                if parent_id and parent_id not in processed_ids:
                    parent_data = self.get_node_data(parent_id)
                    if parent_data and parent_data.get('level') == 1:
                        result_nodes.append(parent_data)
                        processed_ids.add(parent_id)
                
                # 获取level 3子节点
                children_ids = self.get_children_nodes(node_id)
                for child_id in children_ids:
                    if child_id not in processed_ids:
                        child_data = self.get_node_data(child_id)
                        if child_data and child_data.get('level') == 3:
                            result_nodes.append(child_data)
                            processed_ids.add(child_id)
            
            elif level == 3:
                # Level 3节点：添加自身，然后获取level 1和level 2节点
                if node_id not in processed_ids:
                    result_nodes.append(node_data)
                    processed_ids.add(node_id)
                
                # 获取level 2父节点
                parent_id = self.get_parent_node(node_id)
                if parent_id:
                    parent_data = self.get_node_data(parent_id)
                    if parent_data and parent_data.get('level') == 2:
                        if parent_id not in processed_ids:
                            result_nodes.append(parent_data)
                            processed_ids.add(parent_id)
                        
                        # 获取level 1祖父节点
                        grandparent_id = self.get_parent_node(parent_id)
                        if grandparent_id and grandparent_id not in processed_ids:
                            grandparent_data = self.get_node_data(grandparent_id)
                            if grandparent_data and grandparent_data.get('level') == 1:
                                result_nodes.append(grandparent_data)
                                processed_ids.add(grandparent_id)
        
        # 按level分组返回
        grouped_results = {
            'level_1': [],
            'level_2': [],
            'level_3': []
        }
        
        for node in result_nodes:
            level = node.get('level')
            if level == 1:
                grouped_results['level_1'].append(node)
            elif level == 2:
                grouped_results['level_2'].append(node)
            elif level == 3:
                grouped_results['level_3'].append(node)
        
        return grouped_results
    
    def find_nodes_by_level(self, level: int) -> List[Dict[str, Any]]:
        """查找指定level的所有节点"""
        return [node for node in self.raw_data if node.get('level') == level]
    
    def get_node_path(self, node_id: int) -> List[int]:
        """获取从根节点到指定节点的路径"""
        path = []
        current_id = node_id
        
        while current_id is not None:
            path.insert(0, current_id)
            current_id = self.get_parent_node(current_id)
        
        return path
    
    def get_full_subtree(self, node_id: int) -> List[int]:
        """获取指定节点的完整子树（包含所有子孙节点）"""
        result = [node_id]
        
        def collect_children(current_id):
            children = self.get_children_nodes(current_id)
            for child_id in children:
                result.append(child_id)
                collect_children(child_id)  # 递归收集子孙节点
        
        collect_children(node_id)
        return result 