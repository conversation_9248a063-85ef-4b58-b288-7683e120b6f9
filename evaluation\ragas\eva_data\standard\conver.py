import json

input_path = './cache/1_results.json'
output_path = './1.json'

# 读取原始 JSON 文件
with open(input_path, 'r', encoding='utf-8') as f:
    data = json.load(f)  # data 是 list 类型

# 提取字段
extracted_data = []
for item in data:
    extracted_data.append({
        'id': item.get('id'),
        'type': item.get('type'),
        'question': item.get('question'),
        'ground_truth_answers': item.get('answer')  # 如果你想改字段名
    })

# 保存为新 JSON 文件
with open(output_path, 'w', encoding='utf-8') as f:
    json.dump(extracted_data, f, ensure_ascii=False, indent=2)

print("提取完成。")
