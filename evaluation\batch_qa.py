"""
批量问答处理脚本
处理evaluation/questions.json文件，并发执行问答，保存结果
"""

import json
import asyncio
import concurrent.futures
import time
from pathlib import Path
import sys
import argparse
from typing import List, Dict, Any
from tqdm import tqdm

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from service.rag_service import RAGService


class BatchQAProcessor:
    """批量问答处理器"""
    
    def __init__(self, vector_store_type: str = "chroma", max_workers: int = 4):
        """
        初始化批量问答处理器
        
        Args:
            vector_store_type: 向量库类型
            max_workers: 最大并发工作线程数
        """
        self.vector_store_type = vector_store_type
        self.max_workers = max_workers
        self.rag_services = []  # 存储多个RAG服务实例
        
    def initialize_rag_services(self, rebuild_knowledge_base: bool = False, batch_size: int = 1000):
        """
        初始化多个RAG服务实例用于并发处理
        
        Args:
            rebuild_knowledge_base: 是否重新构建知识库
            batch_size: 批处理大小
        """
        print(f"初始化 {self.max_workers} 个RAG服务实例...")
        
        # 创建多个RAG服务实例
        for i in range(self.max_workers):
            print(f"初始化RAG服务实例 {i+1}/{self.max_workers}")
            rag_service = RAGService(vector_store_type=self.vector_store_type)
            rag_service.initialize(
                rebuild_knowledge_base=(rebuild_knowledge_base and i == 0),  # 只有第一个实例重建知识库
                batch_size=batch_size
            )
            self.rag_services.append(rag_service)
        
        print("所有RAG服务实例初始化完成")
    
    def load_questions(self, questions_file: str) -> List[Dict[str, Any]]:
        """
        加载问题数据
        
        Args:
            questions_file: 问题文件路径
            
        Returns:
            问题列表
        """
        print(f"加载问题文件: {questions_file}")
        
        with open(questions_file, 'r', encoding='utf-8') as f:
            questions = json.load(f)
        
        print(f"加载了 {len(questions)} 个问题")
        return questions
    
    def process_single_question(self, question_data: Dict[str, Any], rag_service: RAGService) -> Dict[str, Any]:
        """
        处理单个问题

        Args:
            question_data: 问题数据（包含id, type, question字段）
            rag_service: RAG服务实例

        Returns:
            处理结果（包含原始的id和type字段）
        """
        try:
            question = question_data["question"]
            question_id = question_data.get("id")
            question_type = question_data.get("type")

            # 调用RAG服务进行问答
            result = rag_service.query(question)

            if result["success"]:
                return {
                    "id": question_id,
                    "type": question_type,
                    "question": question,
                    "contexts": result["hierarchical_knowledge_json"],
                    "answer": result["answer"],
                    "success": True,
                }
            else:
                # RAG服务处理失败 - 标记为失败类型
                return {
                    "id": question_id,
                    "type": -1,  # 失败类型标记
                    "original_type": question_type,  # 保存原始type
                    "question": question,
                    "contexts": [],
                    "answer": "",
                    "success": False,
                    "error": result.get("error", "未知错误"),
                    "error_details": {
                        "error_type": "rag_service_error",
                        "error_message": result.get("error", "未知错误"),
                        "llm_response": result.get("llm_response", None)
                    }
                }

        except Exception as e:
            # 处理异常 - 标记为失败类型
            return {
                "id": question_data.get("id"),
                "type": -1,  # 失败类型标记
                "original_type": question_data.get("type"),  # 保存原始type
                "question": question_data.get("question", ""),
                "contexts": [],
                "answer": "",
                "success": False,
                "error": str(e),
                "error_details": {
                    "error_type": "processing_exception",
                    "error_message": str(e),
                    "llm_response": None
                }
            }
   
    def process_questions_concurrent(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        并发处理问题列表
        
        Args:
            questions: 问题列表
            
        Returns:
            处理结果列表
        """
        print(f"开始并发处理 {len(questions)} 个问题，使用 {self.max_workers} 个工作线程")
        
        results = []
        
        # 使用ThreadPoolExecutor进行并发处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 创建任务
            future_to_question = {}
            
            for i, question_data in enumerate(questions):
                # 轮询分配RAG服务实例
                rag_service = self.rag_services[i % len(self.rag_services)]
                future = executor.submit(self.process_single_question, question_data, rag_service)
                future_to_question[future] = (i, question_data)
            
            # 使用tqdm显示进度
            with tqdm(total=len(questions), desc="处理问题", unit="个") as pbar:
                for future in concurrent.futures.as_completed(future_to_question):
                    question_index, question_data = future_to_question[future]
                    try:
                        result = future.result()
                        results.append((question_index, result))
                        pbar.update(1)
                        
                        # 显示处理状态
                        if result["success"]:
                            pbar.set_postfix({"状态": "成功", "问题": question_data["question"][:30] + "..."})
                        else:
                            pbar.set_postfix({"状态": "失败", "错误": result.get("error", "")[:20] + "..."})
                            
                    except Exception as e:
                        error_result = {
                            "question": question_data.get("question", ""),
                            "contexts": [],
                            "answer": "",
                            "ground_truth": question_data.get("answer", ""),
                            "success": False,
                            "error": str(e),
                            "metadata": {
                                "type": question_data.get("type"),
                                "disease_id": question_data.get("disease_id"),
                                "disease_name": question_data.get("disease_name"),
                                "metabolite_id": question_data.get("metabolite_id"),
                                "metabolite_name": question_data.get("metabolite_name")
                            }
                        }
                        results.append((question_index, error_result))
                        pbar.update(1)
                        pbar.set_postfix({"状态": "异常", "错误": str(e)[:20] + "..."})
        
        # 按原始顺序排序结果
        results.sort(key=lambda x: x[0])
        return [result[1] for result in results]
    
    def save_results(self, results: List[Dict[str, Any]], output_file: str):
        """
        保存处理结果
        
        Args:
            results: 处理结果列表
            output_file: 输出文件路径
        """
        print(f"保存结果到: {output_file}")
        
        # 创建输出目录
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 统计信息
        total_questions = len(results)
        successful_questions = sum(1 for r in results if r["success"])
        failed_questions = total_questions - successful_questions
        
        print(f"处理完成:")
        print(f"  总问题数: {total_questions}")
        print(f"  成功处理: {successful_questions}")
        print(f"  处理失败: {failed_questions}")
        print(f"  成功率: {successful_questions/total_questions*100:.2f}%")

    def run_batch_processing(self, questions_file: str, output_file: str,
                           rebuild_knowledge_base: bool = False, batch_size: int = 1000,
                           limit: int = None):
        """
        运行批量处理

        Args:
            questions_file: 问题文件路径
            output_file: 输出文件路径
            rebuild_knowledge_base: 是否重新构建知识库
            batch_size: 批处理大小
            limit: 限制处理的问题数量（用于测试）
        """
        start_time = time.time()

        print("=== 开始批量问答处理 ===")

        # 1. 初始化RAG服务
        self.initialize_rag_services(rebuild_knowledge_base, batch_size)

        # 2. 加载问题
        questions = self.load_questions(questions_file)

        # 3. 限制问题数量（如果指定）
        if limit and limit > 0:
            questions = questions[:limit]
            print(f"限制处理前 {limit} 个问题")

        # 4. 并发处理问题
        results = self.process_questions_concurrent(questions)

        # 5. 保存结果
        self.save_results(results, output_file)

        end_time = time.time()
        total_time = end_time - start_time

        print(f"=== 批量处理完成 ===")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"平均每个问题: {total_time/len(questions):.2f} 秒")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量问答处理系统")
    parser.add_argument("--questions", type=str, default="evaluation/questions.json",
                       help="问题文件路径")
    parser.add_argument("--output", type=str, default="evaluation/batch_qa_results.json",
                       help="输出文件路径")
    parser.add_argument("--rebuild", action="store_true", help="重新构建知识库")
    parser.add_argument("--batch-size", type=int, default=1000, help="批处理大小")
    parser.add_argument("--max-workers", type=int, default=4, help="最大并发工作线程数")
    parser.add_argument("--vector-store", type=str, default="chroma",
                       choices=["faiss", "chroma"], help="向量库类型")
    parser.add_argument("--limit", type=int, help="限制处理的问题数量（用于测试）")

    args = parser.parse_args()

    try:
        # 创建批量处理器
        processor = BatchQAProcessor(
            vector_store_type=args.vector_store,
            max_workers=args.max_workers
        )

        # 运行批量处理
        processor.run_batch_processing(
            questions_file=args.questions,
            output_file=args.output,
            rebuild_knowledge_base=args.rebuild,
            batch_size=args.batch_size,
            limit=args.limit
        )

    except Exception as e:
        print(f"批量处理失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
