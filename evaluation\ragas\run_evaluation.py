import os
import sys
import argparse
from typing import Dict, Any

from data_handler import load_qa_pairs, save_evaluation_results,load_generated_questions
from evaluator import prepare_evaluation_dataset, evaluate_rag_results

def parse_arguments() -> Dict[str, Any]:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='RAG系统评估工具')
    
    parser.add_argument('--input', '-i', type=str, default='eva_data/standard/1.json',
                        help='输入JSON文件路径，包含问题和标准答案')

    parser.add_argument('--input-rag', '-or', type=str, default='eva_data/gpt-4o/1_results.json',
                        help='RAG JSON文件路径')
    
    parser.add_argument('--output-eval', '-oe', type=str, default='eva_result/gpt-4o/1_evaluation_results.json',
                        help='评估结果JSON文件路径')
    
    parser.add_argument('--llm-api-key', type=str, 
                        default="sk-CPGboauHpp1SfH9zpSrwEYSH6SQo2Rb1MpRzm06LSY7mt4QH",
                        help='LLM API密钥')
    
    parser.add_argument('--llm-model', type=str, default="gpt-3.5-turbo",
                        help='LLM模型名称')
    
    parser.add_argument('--llm-base-url', type=str, 
                        default="http://chatapi.littlewheat.com/v1",
                        help='LLM API基础URL')
    
    return vars(parser.parse_args())

def main() -> None:
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    input_file = args['input']
    input_rag_file = args['input_rag']
    output_eval_file = args['output_eval']
    llm_api_key = args['llm_api_key']
    llm_model = args['llm_model']
    llm_base_url = args['llm_base_url']
    
    try:
        # 1. 加载问题和标准答案
        print(f"正在加载问题和标准答案: {input_file}")
        data = load_qa_pairs(input_file)
        questions = data['questions']
        ground_truth_answers = data['ground_truth_answers']
        
        # 2. 加载处理完的RAG结果
        print("正在处理问题...")
        rag_data = load_generated_questions(input_rag_file)
        generated_answers = rag_data['generated_answers']
        retrieved_contexts = rag_data['retrieved_contexts']
  
        
        # 3. 准备评估数据集
        print("正在准备评估数据集...")
        evaluation_dataset = prepare_evaluation_dataset(
            questions,
            ground_truth_answers,
            generated_answers,
            retrieved_contexts
        )
        
        # 5. 执行评估
        print("正在评估结果...")
        evaluation_results = evaluate_rag_results(
            evaluation_dataset,
            llm_api_key,
            llm_model,
            llm_base_url
        )
        
        # 6. 保存评估结果
        print(f"正在保存评估结果: {output_eval_file}")
        print(evaluation_results)
        save_evaluation_results(output_eval_file, evaluation_results)
        
        print("评估完成!")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 