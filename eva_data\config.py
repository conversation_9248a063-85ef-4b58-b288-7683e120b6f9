# 评估问答数据集生成配置

# 每种问题类型的数量配置
QUESTION_COUNTS = {
    1: 100,  # 某个疾病的标志性代谢物是什么
    2: 100,  # xx代谢物xx浓度可能患有哪种疾病（异常浓度）
    3: 100,  # xx代谢物xx浓度，怀疑患有某种疾病还可以补充化验哪些化合物（异常浓度）
    4: 100,  # 某个代谢物的正常浓度和异常浓度是什么
    5: 100,  # xx代谢物xx浓度可能患有哪种疾病（异常浓度）【代谢物名称使用他的同义词】
    6: 100,  # xx代谢物xx浓度，怀疑患有某种疾病还可以补充化验哪些化合物（异常浓度）【代谢物名称使用他的同义词】
    7: 100   # 某个代谢物的正常浓度和异常浓度是什么【代谢物名称使用他的同义词】
}

# 数据目录路径
DATA_DIR = "../data/ms-tree"

# 输出文件路径
OUTPUT_FILE = "generated_questions.json"

# 随机种子
RANDOM_SEED = 42

# 问题类型描述
QUESTION_TYPE_DESCRIPTIONS = {
    1: "某个疾病的标志性代谢物是什么",
    2: "xx代谢物xx浓度可能患有哪种疾病（异常浓度）",
    3: "xx代谢物xx浓度，怀疑患有某种疾病还可以补充化验哪些化合物（异常浓度）",
    4: "某个代谢物的正常浓度和异常浓度是什么",
    5: "xx代谢物xx浓度可能患有哪种疾病（异常浓度）【代谢物名称使用他的同义词】",
    6: "xx代谢物xx浓度，怀疑患有某种疾病还可以补充化验哪些化合物（异常浓度）【代谢物名称使用他的同义词】",
    7: "某个代谢物的正常浓度和异常浓度是什么【代谢物名称使用他的同义词】"
} 