# 断点继续批量问答处理系统

## 概述

这是一个支持断点继续和失败重试的批量问答处理系统，基于原有的 `batch_qa.py` 进行了增强。

## 主要功能

### ✅ 增量保存
- **实时保存**: 每处理一定数量的问题就自动保存结果
- **断点继续**: 程序中断后可以从上次保存的位置继续
- **进度跟踪**: 实时更新处理进度和失败记录
- **内存优化**: 避免在内存中积累大量结果
- **故障恢复**: 最大程度减少因程序中断造成的损失

### ✅ 失败重试
- 自动记录处理失败的问题
- 支持单独重新处理失败的问题
- 提高整体成功率

### ✅ 并发处理
- 多线程并发处理，提高效率
- 可配置工作线程数量
- 实时进度显示

## 使用方法

### 环境准备
```bash
conda activate ms-py_312
```

### 基本命令

#### 1. 快速测试（6个问题）
```bash
python evaluation/run_batch_qa.py test
```

#### 2. 小批量处理（100个问题）
```bash
python evaluation/run_batch_qa.py small
```

#### 3. 完整批量处理（所有问题）
```bash
python evaluation/run_batch_qa.py full
```

#### 4. 重新开始处理（不使用断点继续）
```bash
python evaluation/run_batch_qa.py full --no-resume
```

#### 5. 重试失败的问题
```bash
python evaluation/run_batch_qa.py retry --output evaluation/full_batch_results.json
```

## 文件说明

### 输入文件格式
问题文件需要包含以下字段：
```json
[
  {
    "id": 1,           // 唯一标识符
    "type": 1,         // 问题类型
    "question": "..."  // 问题内容
  }
]
```

### 输出文件格式
对于输出文件 `evaluation/full_batch_results.json`，系统会自动生成：

- **结果文件**: `evaluation/full_batch_results.json`
  ```json
  [
    {
      "id": 1,
      "type": 1,
      "question": "...",
      "contexts": [...],
      "answer": "...",
      "success": true
    }
  ]
  ```
- **失败问题文件**: `evaluation/full_batch_results_failed.json`
  ```json
  [
    {
      "id": 3,
      "type": 2,
      "question": "..."
    }
  ]
  ```

**注意**: 系统不再生成单独的进度文件，进度信息直接从结果文件中的ID推断。

## 工作流程

### 推荐使用流程

1. **首次测试**: 运行 `test` 模式验证功能
2. **性能测试**: 运行 `small` 模式测试性能
3. **完整处理**: 运行 `full` 模式处理所有问题
4. **失败重试**: 如有失败问题，使用 `retry` 模式重新处理

### 增量保存机制

1. **分批处理**: 将问题分成小批次处理，每批次完成后立即保存
2. **保存间隔**: 可配置保存间隔，平衡性能和安全性
   - 测试模式: 每2个问题保存一次
   - 小批量模式: 每10个问题保存一次
   - 完整批量模式: 每20个问题保存一次
   - 重试模式: 每10个问题保存一次
3. **进度跟踪**: 基于结果文件中的ID自动推断进度
4. **断点继续**: 程序中断后自动从上次保存位置继续

### 失败重试机制

1. 处理失败的问题自动保存到 `*_failed.json` 文件
2. 使用 `retry` 模式可以只重新处理失败的问题
3. 成功处理后会更新原始结果文件

## 示例场景

### 场景1：正常处理流程
```bash
# 1. 快速测试
python evaluation/run_batch_qa.py test

# 2. 完整处理
python evaluation/run_batch_qa.py full
```

### 场景2：程序中断后继续
```bash
# 程序中断后，再次运行会自动从断点继续
python evaluation/run_batch_qa.py full
```

### 场景3：重新开始处理
```bash
# 忽略之前的进度，重新开始
python evaluation/run_batch_qa.py full --no-resume
```

### 场景4：处理失败问题
```bash
# 重新处理失败的问题
python evaluation/run_batch_qa.py retry --output evaluation/full_batch_results.json
```

## 技术实现

### 核心类：ResumableBatchProcessor

- 继承原有的 `BatchQAProcessor` 功能
- 添加进度保存和恢复机制
- 实现失败问题的单独处理

### 关键方法

- `save_progress()`: 保存处理进度
- `load_progress()`: 加载处理进度
- `save_failed_questions()`: 保存失败问题
- `load_failed_questions()`: 加载失败问题
- `run_resumable_processing()`: 可恢复的批量处理

## 注意事项

1. **环境要求**: 必须激活 `ms-py_312` 环境
2. **文件权限**: 确保有写入权限创建进度和失败文件
3. **磁盘空间**: 确保有足够空间保存结果文件
4. **网络连接**: 处理过程中需要稳定的网络连接
5. **保存间隔**: 可根据实际情况调整保存间隔
   - 间隔过小会影响性能
   - 间隔过大会增加中断损失
   - 建议根据网络稳定性和处理时间调整

## 故障排除

### 常见问题

1. **模块导入错误**: 确保激活了正确的conda环境
2. **文件权限错误**: 检查输出目录的写入权限
3. **内存不足**: 可以减少 `max_workers` 参数值

### 调试方法

```bash
# 运行测试脚本验证功能
python evaluation/test_resumable.py
```

## 更新日志

- **v1.0**: 基础断点继续功能
- **v1.1**: 添加失败重试机制
- **v1.2**: 优化用户界面和帮助信息
- **v1.3**: 实现增量保存，避免程序中断损失
- **v1.4**: 修复失败记录机制，确保统计准确性
  - 修复失败问题重复累积问题
  - 修复成功率计算错误
  - 优化内存使用，避免累积变量
  - 改进失败问题收集逻辑
- **v1.5**: 基于ID的优化重构
  - 使用唯一ID替代索引进行问题跟踪
  - 支持乱序处理和结果合并
  - 更精确的失败问题匹配
  - 兼容旧版本的索引格式
  - 保存完整的问题元数据（id, type, question）
- **v1.6**: 简化逻辑，移除进度文件
  - 移除单独的 `*_progress.json` 文件
  - 基于结果文件中的ID推断进度
  - 简化文件管理，提高可靠性
  - 减少文件同步问题
