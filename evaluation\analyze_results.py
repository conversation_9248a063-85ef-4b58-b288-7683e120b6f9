"""
批量问答结果分析脚本
分析批量问答的结果，提供统计信息和质量评估
"""

import json
import argparse
from pathlib import Path
from typing import List, Dict, Any
import statistics


class ResultAnalyzer:
    """结果分析器"""
    
    def __init__(self, results_file: str):
        """
        初始化分析器
        
        Args:
            results_file: 结果文件路径
        """
        self.results_file = results_file
        self.results = self.load_results()
    
    def load_results(self) -> List[Dict[str, Any]]:
        """加载结果文件"""
        print(f"加载结果文件: {self.results_file}")
        
        with open(self.results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print(f"加载了 {len(results)} 个结果")
        return results
    
    def basic_statistics(self):
        """基本统计信息"""
        print("\n=== 基本统计信息 ===")
        
        total_questions = len(self.results)
        successful_questions = sum(1 for r in self.results if r.get("success", False))
        failed_questions = total_questions - successful_questions
        
        print(f"总问题数: {total_questions}")
        print(f"成功处理: {successful_questions}")
        print(f"处理失败: {failed_questions}")
        print(f"成功率: {successful_questions/total_questions*100:.2f}%")
        
        # 分析失败原因
        if failed_questions > 0:
            print(f"\n失败问题分析:")
            error_types = {}
            for result in self.results:
                if not result.get("success", False):
                    error = result.get("error", "未知错误")
                    error_types[error] = error_types.get(error, 0) + 1
            
            for error, count in error_types.items():
                print(f"  {error}: {count} 个")
    
    def context_statistics(self):
        """上下文统计信息"""
        print("\n=== 上下文统计信息 ===")
        
        successful_results = [r for r in self.results if r.get("success", False)]
        
        if not successful_results:
            print("没有成功的结果可分析")
            return
        
        context_counts = [len(r.get("contexts", [])) for r in successful_results]
        
        print(f"上下文数量统计:")
        print(f"  平均数量: {statistics.mean(context_counts):.2f}")
        print(f"  中位数: {statistics.median(context_counts):.2f}")
        print(f"  最小值: {min(context_counts)}")
        print(f"  最大值: {max(context_counts)}")
        print(f"  标准差: {statistics.stdev(context_counts):.2f}")
        
        # 上下文数量分布
        print(f"\n上下文数量分布:")
        ranges = [(0, 0), (1, 5), (6, 20), (21, 100), (101, 500), (501, float('inf'))]
        for min_val, max_val in ranges:
            if max_val == float('inf'):
                count = sum(1 for c in context_counts if c > min_val)
                print(f"  {min_val}+: {count} 个")
            else:
                count = sum(1 for c in context_counts if min_val <= c <= max_val)
                print(f"  {min_val}-{max_val}: {count} 个")
    
    def answer_statistics(self):
        """答案统计信息"""
        print("\n=== 答案统计信息 ===")
        
        successful_results = [r for r in self.results if r.get("success", False)]
        
        if not successful_results:
            print("没有成功的结果可分析")
            return
        
        answer_lengths = [len(r.get("answer", "")) for r in successful_results]
        
        print(f"答案长度统计:")
        print(f"  平均长度: {statistics.mean(answer_lengths):.2f} 字符")
        print(f"  中位数: {statistics.median(answer_lengths):.2f} 字符")
        print(f"  最短答案: {min(answer_lengths)} 字符")
        print(f"  最长答案: {max(answer_lengths)} 字符")
        print(f"  标准差: {statistics.stdev(answer_lengths):.2f}")
        
        # 答案长度分布
        print(f"\n答案长度分布:")
        ranges = [(0, 100), (101, 500), (501, 1000), (1001, 2000), (2001, float('inf'))]
        for min_val, max_val in ranges:
            if max_val == float('inf'):
                count = sum(1 for l in answer_lengths if l > min_val)
                print(f"  {min_val}+ 字符: {count} 个")
            else:
                count = sum(1 for l in answer_lengths if min_val <= l <= max_val)
                print(f"  {min_val}-{max_val} 字符: {count} 个")
    
    def entity_statistics(self):
        """实体统计信息"""
        print("\n=== 实体统计信息 ===")
        
        successful_results = [r for r in self.results if r.get("success", False)]
        
        if not successful_results:
            print("没有成功的结果可分析")
            return
        
        compound_counts = []
        disease_counts = []
        
        for result in successful_results:
            metadata = result.get("metadata", {})
            entities = metadata.get("entities", {})
            compound_counts.append(len(entities.get("compounds", [])))
            disease_counts.append(len(entities.get("diseases", [])))
        
        print(f"化合物实体统计:")
        if compound_counts:
            print(f"  平均数量: {statistics.mean(compound_counts):.2f}")
            print(f"  最大数量: {max(compound_counts)}")
            print(f"  检测到化合物的问题: {sum(1 for c in compound_counts if c > 0)} 个")
        
        print(f"\n疾病实体统计:")
        if disease_counts:
            print(f"  平均数量: {statistics.mean(disease_counts):.2f}")
            print(f"  最大数量: {max(disease_counts)}")
            print(f"  检测到疾病的问题: {sum(1 for c in disease_counts if c > 0)} 个")
    
    def question_type_statistics(self):
        """问题类型统计"""
        print("\n=== 问题类型统计 ===")
        
        type_counts = {}
        type_success = {}
        
        for result in self.results:
            metadata = result.get("metadata", {})
            question_type = metadata.get("type", "未知")
            
            type_counts[question_type] = type_counts.get(question_type, 0) + 1
            
            if result.get("success", False):
                type_success[question_type] = type_success.get(question_type, 0) + 1
        
        print(f"问题类型分布:")
        for q_type, count in type_counts.items():
            success_count = type_success.get(q_type, 0)
            success_rate = success_count / count * 100 if count > 0 else 0
            print(f"  类型 {q_type}: {count} 个 (成功率: {success_rate:.2f}%)")
    
    def sample_results(self, num_samples: int = 5):
        """显示样本结果"""
        print(f"\n=== 样本结果 (前{num_samples}个成功案例) ===")
        
        successful_results = [r for r in self.results if r.get("success", False)]
        
        for i, result in enumerate(successful_results[:num_samples]):
            print(f"\n样本 {i+1}:")
            print(f"  问题: {result['question']}")
            print(f"  标准答案: {result['ground_truth']}")
            print(f"  生成答案: {result['answer'][:200]}...")
            print(f"  上下文数量: {len(result.get('contexts', []))}")
            if result.get('contexts'):
                print(f"  示例上下文: {result['contexts'][0]}")
    
    def export_summary(self, output_file: str):
        """导出分析摘要"""
        print(f"\n=== 导出分析摘要到: {output_file} ===")
        
        total_questions = len(self.results)
        successful_questions = sum(1 for r in self.results if r.get("success", False))
        successful_results = [r for r in self.results if r.get("success", False)]
        
        summary = {
            "basic_stats": {
                "total_questions": total_questions,
                "successful_questions": successful_questions,
                "failed_questions": total_questions - successful_questions,
                "success_rate": successful_questions / total_questions * 100 if total_questions > 0 else 0
            }
        }
        
        if successful_results:
            context_counts = [len(r.get("contexts", [])) for r in successful_results]
            answer_lengths = [len(r.get("answer", "")) for r in successful_results]
            
            summary["context_stats"] = {
                "avg_contexts": statistics.mean(context_counts),
                "median_contexts": statistics.median(context_counts),
                "min_contexts": min(context_counts),
                "max_contexts": max(context_counts)
            }
            
            summary["answer_stats"] = {
                "avg_length": statistics.mean(answer_lengths),
                "median_length": statistics.median(answer_lengths),
                "min_length": min(answer_lengths),
                "max_length": max(answer_lengths)
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print("分析摘要导出完成")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print(f"=== 批量问答结果分析 ===")
        print(f"结果文件: {self.results_file}")
        
        self.basic_statistics()
        self.context_statistics()
        self.answer_statistics()
        self.entity_statistics()
        self.question_type_statistics()
        self.sample_results()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量问答结果分析")
    parser.add_argument("results_file", help="结果文件路径")
    parser.add_argument("--export", type=str, help="导出分析摘要到指定文件")
    parser.add_argument("--samples", type=int, default=5, help="显示的样本数量")
    
    args = parser.parse_args()
    
    try:
        analyzer = ResultAnalyzer(args.results_file)
        analyzer.run_full_analysis()
        
        if args.export:
            analyzer.export_summary(args.export)
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
