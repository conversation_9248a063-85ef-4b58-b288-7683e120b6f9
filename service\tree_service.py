"""
代谢物树结构服务核心实现
"""

import json
import os
from typing import Dict, List, Optional, Any, Set
from .tree_models import TreeNode, TreeStructure, DiseaseNode, MetaboliteNode, ConcentrationNode


class MetaboliteTreeService:
    """代谢物树结构服务类"""
    
    def __init__(self):
        self.tree_structure = TreeStructure()
        self.disease_name_to_id: Dict[str, int] = {}
        self.metabolite_accession_to_id: Dict[str, int] = {}
    
    def build_tree_from_json(self, input_file: str) -> bool:
        """
        从JSON文件构建树结构
        """
        try:
            print("正在读取代谢物数据...")
            with open(input_file, 'r', encoding='utf-8') as f:
                metabolites_data = json.load(f)
            
            print(f"开始构建树结构，共 {len(metabolites_data)} 个代谢物...")
            
            processed_count = 0
            for metabolite_data in metabolites_data:
                self._process_metabolite(metabolite_data)
                processed_count += 1
                
                if processed_count % 5000 == 0:
                    print(f"已处理 {processed_count} 个代谢物...")
            
            print(f"树结构构建完成！共处理 {processed_count} 个代谢物")
            return True
            
        except Exception as e:
            print(f"构建树结构时出错: {e}")
            return False
    
    def _process_metabolite(self, metabolite_data: Dict[str, Any]):
        """处理单个代谢物数据"""
        # 创建代谢物节点
        metabolite_id = self.tree_structure.get_next_id()
        metabolite_node = TreeNode(
            id=metabolite_id,
            level=2,
            name=metabolite_data.get('name', ''),
            node_type='metabolite'
        )
        
        # 存储代谢物详细信息
        metabolite_detail = MetaboliteNode(
            id=metabolite_id,
            name=metabolite_data.get('name', ''),
            description=metabolite_data.get('description', ''),
            accession=metabolite_data.get('accession', ''),
            synonyms=metabolite_data.get('synonyms', [])
        )
        
        self.tree_structure.add_node(metabolite_node)
        self.tree_structure.raw_data.append(metabolite_detail.to_raw_data())
        
        # 处理同义词映射
        if metabolite_detail.synonyms:
            self.tree_structure.synonyms_mapping.append({
                'synonyms': metabolite_detail.synonyms,
                'id': metabolite_id
            })
        
        # 记录代谢物映射
        if metabolite_detail.accession:
            self.metabolite_accession_to_id[metabolite_detail.accession] = metabolite_id
        
        # 处理疾病数据
        diseases = metabolite_data.get('diseases', [])
        for disease_data in diseases:
            disease_node = self._get_or_create_disease_node(disease_data)
            disease_node.add_child(metabolite_node)
        
        # 处理正常浓度数据
        normal_concentrations = metabolite_data.get('normal_concentrations', [])
        for concentration_data in normal_concentrations:
            concentration_node = self._create_concentration_node(concentration_data, 'normal')
            metabolite_node.add_child(concentration_node)
        
        # 处理异常浓度数据
        abnormal_concentrations = metabolite_data.get('abnormal_concentrations', [])
        for concentration_data in abnormal_concentrations:
            concentration_node = self._create_concentration_node(concentration_data, 'abnormal')
            metabolite_node.add_child(concentration_node)
    
    def _get_or_create_disease_node(self, disease_data: Dict[str, Any]) -> TreeNode:
        """获取或创建疾病节点"""
        disease_name = disease_data.get('name', '').strip()
        
        # 检查是否已存在该疾病
        if disease_name in self.disease_name_to_id:
            disease_id = self.disease_name_to_id[disease_name]
            return self.tree_structure.get_node(disease_id)
        
        # 创建新的疾病节点
        disease_id = self.tree_structure.get_next_id()
        disease_node = TreeNode(
            id=disease_id,
            level=1,
            name=disease_name,
            node_type='disease'
        )
        
        # 处理references字段，统一转换为数组格式
        references = self._normalize_references(disease_data.get('references', {}))
        
        disease_detail = DiseaseNode(
            id=disease_id,
            name=disease_name,
            references=references
        )
        
        self.tree_structure.add_node(disease_node)
        self.tree_structure.raw_data.append(disease_detail.to_raw_data())
        self.disease_name_to_id[disease_name] = disease_id
        
        return disease_node
    
    def _create_concentration_node(self, concentration_data: Dict[str, Any], concentration_type: str) -> TreeNode:
        """创建浓度节点"""
        concentration_id = self.tree_structure.get_next_id()
        concentration_node = TreeNode(
            id=concentration_id,
            level=3,
            name=f"{concentration_type}_concentration",
            node_type='concentration'
        )
        
        # 处理references字段
        references = self._normalize_references(concentration_data.get('references', {}))
        
        # 创建浓度详细信息
        concentration_detail = ConcentrationNode(
            id=concentration_id,
            concentration_type=concentration_type,
            biospecimen=concentration_data.get('biospecimen', ''),
            concentration_value=concentration_data.get('concentration_value'),
            concentration_units=concentration_data.get('concentration_units'),
            references=references
        )
        
        # 根据类型设置不同的字段
        if concentration_type == 'normal':
            concentration_detail.subject_age = concentration_data.get('subject_age', '')
            concentration_detail.subject_sex = concentration_data.get('subject_sex', '')
            concentration_detail.subject_condition = concentration_data.get('subject_condition', '')
        else:  # abnormal
            concentration_detail.patient_age = concentration_data.get('patient_age', '')
            concentration_detail.patient_sex = concentration_data.get('patient_sex', '')
            concentration_detail.patient_information = concentration_data.get('patient_information', '')
            concentration_detail.comment = concentration_data.get('comment', '')
        
        self.tree_structure.add_node(concentration_node)
        self.tree_structure.raw_data.append(concentration_detail.to_raw_data())
        
        return concentration_node
    
    def _normalize_references(self, references_data: Any) -> List[Dict[str, str]]:
        """标准化references字段为数组格式"""
        if not references_data:
            return []
        
        if isinstance(references_data, dict):
            reference = references_data.get('reference', [])
            if isinstance(reference, dict):
                # 单个reference对象
                return [reference]
            elif isinstance(reference, list):
                # reference数组
                return reference
            else:
                return []
        
        return []
    
    def get_by_id(self, node_id: int) -> Optional[TreeNode]:
        """根据ID获取节点"""
        return self.tree_structure.get_node(node_id)
    
    def navigate(self, node_id: int, direction: int) -> List[TreeNode]:
        """
        层级导航
        direction: -1 向上检索一层, 1 向下检索一层
        """
        return self.tree_structure.navigate(node_id, direction)
    
    def save_to_files(self, output_dir: str):
        """保存树结构到文件"""
        try:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存原始数据文件
            raw_data_path = os.path.join(output_dir, 'raw_data.json')
            with open(raw_data_path, 'w', encoding='utf-8') as f:
                json.dump(self.tree_structure.raw_data, f, ensure_ascii=False, indent=2)
            print(f"原始数据已保存到: {raw_data_path}")
            
            # 保存树结构文件
            tree_structure_path = os.path.join(output_dir, 'tree_structure.json')
            tree_json = self.tree_structure.to_tree_json()
            with open(tree_structure_path, 'w', encoding='utf-8') as f:
                json.dump(tree_json, f, ensure_ascii=False, indent=2)
            print(f"树结构已保存到: {tree_structure_path}")
            
            # 保存同义词映射文件
            synonyms_path = os.path.join(output_dir, 'synonyms_mapping.json')
            with open(synonyms_path, 'w', encoding='utf-8') as f:
                json.dump(self.tree_structure.synonyms_mapping, f, ensure_ascii=False, indent=2)
            print(f"同义词映射已保存到: {synonyms_path}")
            
            # 打印统计信息
            self._print_statistics()
            
        except Exception as e:
            print(f"保存文件时出错: {e}")
    
    def _print_statistics(self):
        """打印树结构统计信息"""
        total_nodes = len(self.tree_structure.nodes)
        disease_count = len([n for n in self.tree_structure.nodes.values() if n.level == 1])
        metabolite_count = len([n for n in self.tree_structure.nodes.values() if n.level == 2])
        concentration_count = len([n for n in self.tree_structure.nodes.values() if n.level == 3])
        
        print(f"\n=== 树结构统计信息 ===")
        print(f"总节点数: {total_nodes}")
        print(f"疾病节点数: {disease_count}")
        print(f"代谢物节点数: {metabolite_count}")
        print(f"浓度节点数: {concentration_count}")
        print(f"同义词映射数: {len(self.tree_structure.synonyms_mapping)}") 