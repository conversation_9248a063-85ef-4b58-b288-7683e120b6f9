"""
LLM服务接口 - 封装langchain和OpenAI调用
"""

from typing import List, Dict, Any, Optional
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from config import Config
import json
import re
import time


class LLMService:
    """LLM服务类"""

    def __init__(self):
        """初始化LLM服务"""
        self.llm = ChatOpenAI(
            base_url=Config.get("LLMs_BASE_URL"),
            api_key=Config.get("LLMs_API_KEY"),
            model=Config.get("LLMs_MODEL_NAME"),
            temperature=0.1,
            timeout = 60,
            request_timeout=60 
        )
        self.cheap_llm = ChatOpenAI(
            base_url=Config.get("LLMs_CHEAP_BASE_URL"),
            api_key=Config.get("LLMs_CHEAP_API_KEY"),
            model=Config.get("LLMs_CHEAP_MODEL_NAME"),
            temperature=0.1,
            timeout = 60,
            request_timeout=60 
        )

    def _retry_llm_call(self, llm_instance, messages, max_retries=3, base_delay=1):
        """
        带重试机制的LLM调用

        Args:
            llm_instance: LLM实例
            messages: 消息列表
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）

        Returns:
            LLM响应
        """
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                response = llm_instance.invoke(messages)
                return response

            except Exception as e:
                last_error = e
                error_str = str(e).lower()

                # 检查是否是可重试的错误
                if any(code in error_str for code in ['504', '502', '503', '500', 'timeout', 'connection']):
                    if attempt < max_retries:
                        delay = base_delay * (2 ** attempt)  # 指数退避
                        print(f"LLM调用失败 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)[:100]}...")
                        print(f"等待 {delay} 秒后重试...")
                        time.sleep(delay)
                        continue

                # 非可重试错误，直接抛出
                raise e

        # 所有重试都失败了
        raise last_error
    
    def extract_entities(self, query: str) -> Dict[str, List[str]]:
        """
        从用户问题中抽取化合物和疾病实体
        
        Args:
            query: 用户问题
            
        Returns:
            包含化合物和疾病的字典
        """
        system_prompt = """
        You are a biomedical named entity recognition expert. Please extract **standard scientific terms** for **compounds** (including metabolites, drugs, biochemical molecules, etc.) and **diseases** (including diseases, disorders, syndromes, etc.) from the user's question.

        Strictly return results in the following JSON format:
        {
        "compounds": ["Compound1", "Compound2"],
        "diseases": ["Disease1", "Disease2"]
        }

        Instructions:

        If no entities of a certain type are found, return an empty array;

        Return JSON only, without any additional text, explanation, or comments;

        Entity names should use standardized biomedical terminology ;

        """

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"User question: {query}")
        ]
        
        try:
            response = self._retry_llm_call(self.cheap_llm, messages)
            result_text = response.content.strip()
            
            # 尝试解析JSON
            try:
                # 清理可能的markdown格式
                if "```json" in result_text:
                    result_text = result_text.split("```json")[1].split("```")[0].strip()
                elif "```" in result_text:
                    result_text = result_text.split("```")[1].strip()
                
                result = json.loads(result_text)
                
                # 确保返回格式正确
                return {
                    "compounds": result.get("compounds", []),
                    "diseases": result.get("diseases", [])
                }
                
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试使用正则表达式提取
                print(f"实体抽取-JSON解析失败，原始响应: {result_text}")
                raise
                return {"compounds": [], "diseases": []}
                
        except Exception as e:
            print(f"实体抽取失败: {e}")
            raise
            
            return {"compounds": [], "diseases": []}
    

    def decision_path(self, query: str,hierarchical_knowledge: List[Dict]) -> list:
        """ 
        调用大模型获得推理路径
        """
        system_prompt = """
            You are a biomedical question-answering expert, specializing in the retrieval and analysis of knowledge related to **metabolites, diseases, and their concentration data**.

            ## Knowledge Base Structure:
            - **Level 1**: Diseases (root nodes)  
            - **Level 2**: Metabolites (child nodes of diseases)  
            - **Level 3**: Concentration data (child nodes of metabolites, includes both normal and abnormal values)

            ---

            ## Your Task:
            The user will ask you a biomedical-related question. Based on the question content, you need to determine which **levels (Level)** and **nodes** should be queried.

            You will be given a **"list of candidate nodes"**, where each node contains the following fields:
            - `id`: Unique identifier
            - `level`: The node's own level (1, 2, or 3)
            - `name`, `description`: Node name and description

            You must complete the following steps:

            1. **Identify and exclude irrelevant nodes**  
            2. **For relevant nodes, determine which level(s) the user is interested in**  
            (e.g., metabolite definition, associated disease, concentration data, etc.)

            ---

            ## Output Format Requirement:

            Please strictly follow the JSON array format below. Each object represents a node to be queried and the levels of interest:

            ```json
            [
            {
                "id": NodeID,
                "query": [Levels to query, e.g., 1 or 3, can be multiple]
            }
            ]
            ````

            ---

            ## Example:

            **User Question**: What is 1-Methylhistidine?
            **Candidate Nodes (from knowledge base)**:

            [
            {"id": 129001, "level": 2, "name": "...", "description": "..."},
            {"id": 28598, "level": 1, "name": "Sarcoma"},
            ...
            ]
   

            **Your output should be**:

            ```json
            [
            {"id": 129001, "query": [2]}
            ]
            ```

            ---

            ## Notes:

            * `level` refers to the node’s original level in the hierarchy. It is **not** the same as what the user wants to query.
            * `query` refers to the level(s) the **user wants to retrieve information from** (can be any combination of 1, 2, 3).
            * Completely exclude any nodes **not relevant** to the user's question.
            * The user may care about multiple levels at once (e.g., disease background + abnormal concentration → `[1, 3]`).
            * Output must be a **pure JSON array** with no extra explanation or commentary.
        """

        user_content = f"""
        **User Question**: {query}

        **Candidate Nodes (from knowledge base)**:

        {hierarchical_knowledge}

        """

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_content)
        ]

        try:
            response = self._retry_llm_call(self.cheap_llm, messages)
            result_text = response.content.strip()
            # 尝试解析JSON
            try:
                # 清理可能的markdown格式
                if "```json" in result_text:
                    result_text = result_text.split("```json")[1].split("```")[0].strip()
                elif "```" in result_text:
                    result_text = result_text.split("```")[1].strip()
                
                result = json.loads(result_text)
                
                
                return result
                
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试使用正则表达式提取
                print(f"推理路径-JSON解析失败，原始响应: {result_text}")
                raise

        except Exception as e:
            print(f"推理路径获取失败: {e}")
            raise
        
        




    def generate_answer_hierarchical(self, query: str, hierarchical_knowledge: List[Dict]) -> str:
        """
        根据层级化的知识结构生成回答
        
        Args:
            query: 用户问题
            hierarchical_knowledge: 层级化的知识结构
            
        Returns:
            生成的答案
        """
        hierarchical_context = hierarchical_knowledge
        
        system_prompt = """
        **You are a biomedical question-answering expert specializing in metabolites, diseases, and biochemical concentration data.**

        Please answer the user's question **based solely on the provided hierarchical medical knowledge base**, following the rules below:

        1. **Use only the information from the knowledge base.** Do not add or fabricate any content beyond what is provided.  
        2. **If relevant information is missing, clearly state “No relevant information provided.”** Avoid speculation or assumptions.  
        3. Your response should be **accurate, professional, and easy to understand**, targeting users with a medical or research background.  
        4. If the knowledge base contains **specific data, numerical values, or research findings**, you may cite them appropriately to support your response.  
        5. **Leverage the hierarchical structure** (Disease → Metabolite → Concentration) to clearly present the relationships among entities.  
        6. If literature references are included in the knowledge base, **explicitly cite them** in your response .  
        7. **Avoid phrases like** “Based on the knowledge base,” “From the provided information,” or “It can be seen that.” Instead, state the facts directly and clearly.

        ---

        **Knowledge Structure:**

        - **Level 1**: Disease information (root level)  
        - **Level 2**: Associated metabolites (child level)  
        - **Level 3**: Concentration data (including normal and abnormal values)  

        ---

        **Recommendation**: Present your answer in **structured paragraphs or bullet points** to enhance readability and facilitate quick access to key information.

        """

        user_content = f"""
        **User Question:**  
            {query}

        Please answer the question using the following hierarchical medical knowledge base information.  
        **Only use the information provided in the knowledge base. Do not fabricate or infer any additional content.**

            {hierarchical_context}

        **Important:**  
        Make sure the response **does NOT** include any of the following phrases:  
        "According to the knowledge base",  
        "Based on the provided information",  
        "As shown above", or similar expressions.  
        Please state the facts directly.

        """


        messages = [ 
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_content)
        ]
        
        try:
            response = self._retry_llm_call(self.llm, messages)
            print(f"LLM回答: {response.content[:200]}...")
            return response.content.strip()

        except Exception as e:
            print(f"答案生成失败: {e}")
            raise
            return "抱歉，我无法生成回答。请检查您的问题或稍后再试。"
    

    def test_connection(self) -> bool:
        """测试LLM连接"""
        try:
            response = self.llm.invoke([HumanMessage(content="Hello")])
            return True
        except Exception as e:
            print(f"LLM连接测试失败: {e}")
            return False 