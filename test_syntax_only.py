#!/usr/bin/env python3
"""
仅测试语法的简单测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 仅导入必要的模块进行语法检查
try:
    import ast
    
    # 检查rag_service.py的语法
    with open('service/rag_service.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    # 尝试解析AST
    ast.parse(code)
    print("✓ service/rag_service.py 语法检查通过")
    
    # 检查新方法是否存在
    if '_build_hierarchical_knowledge_by_query' in code:
        print("✓ 新方法 _build_hierarchical_knowledge_by_query 已添加")
    else:
        print("✗ 新方法 _build_hierarchical_knowledge_by_query 未找到")
    
    # 检查方法调用是否已更新
    if 'self._build_hierarchical_knowledge_by_query(decision_path_result)' in code:
        print("✓ 方法调用已更新为使用新方法")
    else:
        print("✗ 方法调用未更新")
    
    print("\n语法检查完成，代码结构正确！")
    
except SyntaxError as e:
    print(f"✗ 语法错误: {e}")
    print(f"  行号: {e.lineno}")
    print(f"  位置: {e.offset}")
except Exception as e:
    print(f"✗ 检查过程中发生错误: {e}")
