"""
数据模型定义
"""

from typing import List, Dict, Optional, Any, Union
from dataclasses import dataclass, field


@dataclass
class TreeNode:
    """树节点数据模型"""
    id: int
    level: int
    name: str
    node_type: str = ""  # disease, metabolite, concentration
    children: List['TreeNode'] = field(default_factory=list)
    data: Dict[str, Any] = field(default_factory=dict)
    
    def add_child(self, child: 'TreeNode'):
        """添加子节点"""
        if child not in self.children:
            self.children.append(child)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式用于JSON序列化"""
        return {
            'level': self.level,
            'id': self.id,
            'child': [child.to_dict() for child in self.children]
        }


@dataclass  
class DiseaseNode:
    """疾病节点数据"""
    id: int
    level: int = 1
    name: str = ""
    references: List[Dict[str, str]] = field(default_factory=list)
    
    def to_raw_data(self) -> Dict[str, Any]:
        """转换为原始数据格式"""
        return {
            'id': self.id,
            'level': self.level,
            'name': self.name,
            'references': self.references
        }


@dataclass
class MetaboliteNode:
    """代谢物节点数据"""
    id: int
    level: int = 2
    name: str = ""
    description: str = ""
    accession: str = ""
    synonyms: List[str] = field(default_factory=list)
    
    def to_raw_data(self) -> Dict[str, Any]:
        """转换为原始数据格式"""
        return {
            'id': self.id,
            'level': self.level,
            'name': self.name,
            'description': self.description,
            'accession': self.accession
        }


@dataclass
class ConcentrationNode:
    """浓度节点数据"""
    id: int
    level: int = 3
    concentration_type: str = ""  # normal 或 abnormal
    biospecimen: str = ""
    concentration_value: Optional[str] = None
    concentration_units: Optional[str] = None
    subject_age: str = ""
    subject_sex: str = ""
    subject_condition: str = ""
    patient_age: str = ""
    patient_sex: str = ""
    patient_information: str = ""
    comment: str = ""
    references: List[Dict[str, str]] = field(default_factory=list)
    
    def to_raw_data(self) -> Dict[str, Any]:
        """转换为原始数据格式"""
        data = {
            'id': self.id,
            'level': self.level,
            'type': self.concentration_type,
            'biospecimen': self.biospecimen,
            'concentration_value': self.concentration_value,
            'concentration_units': self.concentration_units,
            'references': self.references
        }
        
        # 根据类型添加相应字段
        if self.concentration_type == 'normal':
            data.update({
                'subject_age': self.subject_age,
                'subject_sex': self.subject_sex,
                'subject_condition': self.subject_condition
            })
        else:  # abnormal
            data.update({
                'patient_age': self.patient_age,
                'patient_sex': self.patient_sex,
                'patient_information': self.patient_information,
                'comment': self.comment
            })
        
        return data


class TreeStructure:
    """树结构管理类"""
    
    def __init__(self):
        self.nodes: Dict[int, TreeNode] = {}
        self.raw_data: List[Dict[str, Any]] = []
        self.synonyms_mapping: List[Dict[str, Any]] = []
        self.current_id = 1
    
    def get_next_id(self) -> int:
        """获取下一个可用ID"""
        next_id = self.current_id
        self.current_id += 1
        return next_id
    
    def add_node(self, node: TreeNode):
        """添加节点到结构中"""
        self.nodes[node.id] = node
    
    def get_node(self, node_id: int) -> Optional[TreeNode]:
        """根据ID获取节点"""
        return self.nodes.get(node_id)
    
    def navigate(self, node_id: int, direction: int) -> List[TreeNode]:
        """
        层级导航
        direction: -1 向上检索一层, 1 向下检索一层
        """
        node = self.get_node(node_id)
        if not node:
            return []
        
        if direction == 1:  # 向下
            return node.children
        elif direction == -1:  # 向上
            # 查找父节点
            for parent_node in self.nodes.values():
                if node in parent_node.children:
                    return [parent_node]
            return []
        else:
            return []
    
    def to_tree_json(self) -> List[Dict[str, Any]]:
        """转换为树结构JSON格式"""
        # 找到所有根节点（level 1的节点）
        root_nodes = [node for node in self.nodes.values() if node.level == 1]
        return [node.to_dict() for node in root_nodes] 