import json
import os
import re
from collections import defaultdict

# 加载原始数据
with open('generated_questions.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 按 type 分组
type_groups = defaultdict(list)
for item in data:
    type_groups[item['type']].append(item)

# 创建输出目录
output_dir = 'type_outputs'
os.makedirs(output_dir, exist_ok=True)

# 初始化全局 id
global_id = 1

# 遍历每个 type 分组
for type_name, items in type_groups.items():
    top_50 = items[:50]

    # 构建新的仅含 id, type, question 的数据
    processed_items = []
    for item in top_50:
        processed_items.append({
            "id": global_id,
            "type": item["type"],
            "question": item["question"]
        })
        global_id += 1

    # 安全地构造文件名
    safe_type_name = re.sub(r'[\\/*?:"<>|]', "_", str(type_name))
    filename = f"{safe_type_name}.json"
    filepath = os.path.join(output_dir, filename)

    # 保存为 JSON 文件
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(processed_items, f, indent=2, ensure_ascii=False)
