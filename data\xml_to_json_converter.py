"""
    HMDB 数据  xml转json
"""

import xmltodict
import json
import os

def convert_xml_to_json(xml_file_path, json_file_path):
    """
    Converts an XML file to a JSON file.

    Args:
        xml_file_path (str): The path to the input XML file.
        json_file_path (str): The path to the output JSON file.
    """
    print(f"正在读取 XML 文件: {xml_file_path}")
    try:
        with open(xml_file_path, 'r', encoding='utf-8') as xml_file:
            xml_content = xml_file.read()
    except FileNotFoundError:
        print(f"错误：找不到输入文件 {xml_file_path}")
        return
    except Exception as e:
        print(f"读取 XML 文件时出错: {e}")
        return

    print("正在将 XML 转换为 Python 字典...")
    try:
        # 使用 xmltodict 将 XML 解析为字典
        # process_namespaces=True 可以保留命名空间信息，对于简单转换可以省略
        data_dict = xmltodict.parse(xml_content)
    except Exception as e:
        print(f"解析 XML 时出错: {e}")
        return

    print(f"正在将字典写入 JSON 文件: {json_file_path}")
    try:
        # 创建输出目录
        os.makedirs(os.path.dirname(json_file_path), exist_ok=True)

        with open(json_file_path, 'w', encoding='utf-8') as json_file:
            # 将字典转换为 JSON 字符串并写入文件
            # indent=4 使 JSON 文件格式化，易于阅读
            # ensure_ascii=False 确保非 ASCII 字符（如中文）正确显示
            json.dump(data_dict, json_file, ensure_ascii=False, indent=4)
        print("转换成功！")
    except Exception as e:
        print(f"写入 JSON 文件时出错: {e}")

if __name__ == "__main__":
    # ---- 配置 ----
    input_xml_file = "./processed_data/hmdb_metabolites.xml"  # 输入的 XML 文件名
    output_json_file = "./processed_data/hmdb_metabolites.json" 
    # --------------

    # 获取脚本所在的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # 构建完整的文件路径
    xml_full_path = os.path.join(script_dir, input_xml_file)
    json_full_path = os.path.join(script_dir, output_json_file)

    # 执行转换
    convert_xml_to_json(xml_full_path, json_full_path) 