"""
知识库构建器 - 构建语义超图库和节点库
"""

import json
import os
from typing import List, Dict, Any, Optional
from utils.faiss_vector_store_utils import FAISSVectorStoreUtils
from tqdm import tqdm
from config import Config


class KnowledgeBuilder:
    """知识库构建器"""
    
    def __init__(self, vector_store_type: str = "faiss"):
        """
        初始化知识库构建器
        
        Args:
            persist_directory: 向量库持久化目录
            vector_store_type: 向量库类型 ("faiss" 或 "chroma")
        """
        self.persist_directory = Config.get("VECTOR_DB_PATH")
        if self.persist_directory is None:
            raise ValueError("向量库持久化目录未配置")
        self.vector_store_type = vector_store_type.lower()
        self.semantic_hypergraph_store = None
        self.node_store = None
        
        # 数据存储
        self.raw_data = []
        self.synonyms_mapping = []
        self.tree_structure = []
    
    def _create_vector_store(self, collection_name: str):
        """创建向量存储实例"""
        if self.vector_store_type == "faiss":
            return FAISSVectorStoreUtils(
                persist_directory=os.path.join(self.persist_directory, collection_name),
                collection_name=collection_name
            )
        elif self.vector_store_type == "chroma":
            from utils.vector_store_utils import VectorStoreUtils
            return VectorStoreUtils(
                persist_directory=os.path.join(self.persist_directory, collection_name),
                collection_name=collection_name
            )
        else:
            raise ValueError(f"不支持的向量库类型: {self.vector_store_type}")
        
    def load_data(self, raw_data_path: str, synonyms_path: str, tree_structure_path: str):
        """加载数据文件"""
        print("正在加载数据文件...")
        
        # 加载原始数据
        with open(raw_data_path, 'r', encoding='utf-8') as f:
            self.raw_data = json.load(f)
        print(f"已加载 {len(self.raw_data)} 条原始数据")
        
        # 加载同义词映射
        with open(synonyms_path, 'r', encoding='utf-8') as f:
            self.synonyms_mapping = json.load(f)
        print(f"已加载 {len(self.synonyms_mapping)} 条同义词映射")
        
        # 加载树结构
        with open(tree_structure_path, 'r', encoding='utf-8') as f:
            self.tree_structure = json.load(f)
        print(f"已加载树结构数据")
        
    def build_semantic_hypergraph_store(self, batch_size: int = 1000) -> FAISSVectorStoreUtils:
        """构建语义超图库"""
        print("开始构建语义超图库...")
        
        # 初始化向量存储
        self.semantic_hypergraph_store = self._create_vector_store("semantic_hypergraph")
        
        batch_texts = []
        batch_metadatas = []
        total_added = 0
        
        # 使用tqdm显示进度
        with tqdm(total=len(self.raw_data), desc="构建语义超图库", unit="条") as pbar:
            for item in self.raw_data:
                level = item.get('level')
                item_id = item.get('id')
                text = ""
                metadata = {}
                
                if level == 1:
                    # Level 1: 向量化name，metadata存储id、level、name、references
                    text = item.get('name') or ""
                    references = item.get('references', [])
                    # 将references转换为字符串，使用|分隔
                    references_str = "|".join([
                        f"{ref.get('reference_text', '') or ''}[PMID:{ref.get('pubmed_id', 'None') or 'None'}]" 
                        for ref in references
                    ]) if references else ""
                    
                    metadata = {
                        'id': item_id,
                        'level': level,
                        'name': item.get('name') or "",
                        'references': references_str,
                        'references_count': len(references) if references else 0
                    }
                    
                elif level == 2:
                    # Level 2: 向量化description，metadata存储id、level、name、description
                    text = item.get('description') or ""
                    metadata = {
                        'id': item_id,
                        'level': level,
                        'name': item.get('name') or "",
                        'description': item.get('description') or ""
                    }
                    
                elif level == 3:
                    # Level 3: 根据type构建不同的文本
                    if item.get('type') == 'normal':
                        # 向量化拼接: An {subject_age} {subject_sex} provided a {biospecimen} sample with a concentration of {concentration_value}{concentration_units}.
                        subject_age = item.get('subject_age') or 'unknown age'
                        subject_sex = item.get('subject_sex') or 'unknown sex'
                        biospecimen = item.get('biospecimen') or 'unknown biospecimen'
                        concentration_value = item.get('concentration_value') or 'unknown'
                        concentration_units = item.get('concentration_units') or ''
                        
                        text = f"An {subject_age} {subject_sex} provided a {biospecimen} sample with a concentration of {concentration_value}{concentration_units}."
                        
                    elif item.get('type') == 'abnormal':
                        # 向量化拼接: An {patient_age} {patient_sex} with {patient_information} provided a {biospecimen} sample. The concentration was {concentration_value}{concentration_units}. {comment}
                        patient_age = item.get('patient_age') or 'unknown age'
                        patient_sex = item.get('patient_sex') or 'unknown sex'
                        patient_information = item.get('patient_information') or 'unknown condition'
                        biospecimen = item.get('biospecimen') or 'unknown biospecimen'
                        concentration_value = item.get('concentration_value') or 'unknown'
                        concentration_units = item.get('concentration_units') or ''
                        comment = item.get('comment') or ''
                        
                        text = f"An {patient_age} {patient_sex} with {patient_information} provided a {biospecimen} sample. The concentration was {concentration_value}{concentration_units}. {comment}"
                    
                    else:
                        pbar.update(1)
                        continue  # 跳过未知类型
                    
                    # metadata存储简化字段（避免复杂类型）
                    references = item.get('references', [])
                    # 将references转换为字符串，使用|分隔
                    references_str = "|".join([
                        f"{ref.get('reference_text', '') or ''}[PMID:{ref.get('pubmed_id', 'None') or 'None'}]" 
                        for ref in references
                    ]) if references else ""
                    
                    metadata = {
                        'id': item_id,
                        'level': level,
                        'type': item.get('type') or '',
                        'biospecimen': item.get('biospecimen') or '',
                        'concentration_value': str(item.get('concentration_value') or ''),
                        'concentration_units': item.get('concentration_units') or '',
                        'references': references_str,
                        'references_count': len(references) if references else 0
                    }
                    
                    # 根据类型添加相应字段
                    if item.get('type') == 'normal':
                        metadata.update({
                            'subject_age': item.get('subject_age') or '',
                            'subject_sex': item.get('subject_sex') or '',
                            'subject_condition': item.get('subject_condition') or ''
                        })
                    elif item.get('type') == 'abnormal':
                        metadata.update({
                            'patient_age': item.get('patient_age') or '',
                            'patient_sex': item.get('patient_sex') or '',
                            'patient_information': item.get('patient_information') or '',
                            'comment': item.get('comment') or ''
                        })
                    
                else:
                    pbar.update(1)
                    continue  # 跳过未知level
                
                # 只添加非空文本
                if text.strip():
                    batch_texts.append(text)
                    batch_metadatas.append(metadata)
                    
                    # 当达到批次大小时，批量添加到向量库
                    if len(batch_texts) >= batch_size:
                        self.semantic_hypergraph_store.add_documents(batch_texts, batch_metadatas)
                        total_added += len(batch_texts)
                        pbar.set_postfix({"已添加": total_added})
                        batch_texts.clear()
                        batch_metadatas.clear()
                
                pbar.update(1)
            
            # 添加剩余的数据
            if batch_texts:
                self.semantic_hypergraph_store.add_documents(batch_texts, batch_metadatas)
                total_added += len(batch_texts)
        
        print(f"语义超图库构建完成！共添加 {total_added} 条记录")
        return self.semantic_hypergraph_store
    
    def build_node_store(self, batch_size: int = 1000) -> FAISSVectorStoreUtils:
        """构建节点库"""
        print("开始构建节点库...")
        
        # 初始化向量存储
        self.node_store = self._create_vector_store("node_store")
        
        batch_texts = []
        batch_metadatas = []
        total_added = 0
        
        # 计算总数据量
        node_count = len([item for item in self.raw_data if item.get('level') in [1, 2]])
        synonym_count = sum(len(mapping.get('synonyms', [])) for mapping in self.synonyms_mapping)
        total_count = node_count + synonym_count
        
        with tqdm(total=total_count, desc="构建节点库", unit="条") as pbar:
            # 处理level 1和2的节点name
            for item in self.raw_data:
                level = item.get('level')
                if level in [1, 2]:
                    name = item.get('name') or ''
                    if name.strip():
                        batch_texts.append(name)
                        batch_metadatas.append({
                            'id': item.get('id'),
                            'name': name,
                            'level': level,
                            'source': 'node_name'
                        })
                        
                        # 批量添加检查
                        if len(batch_texts) >= batch_size:
                            self.node_store.add_documents(batch_texts, batch_metadatas)
                            total_added += len(batch_texts)
                            pbar.set_postfix({"已添加": total_added})
                            batch_texts.clear()
                            batch_metadatas.clear()
                    
                    pbar.update(1)
            
            # 处理synonyms_mapping中的同义词
            for mapping in self.synonyms_mapping:
                node_id = mapping.get('id')
                synonyms = mapping.get('synonyms', [])
                
                for synonym in synonyms:
                    if synonym.strip():
                        batch_texts.append(synonym)
                        batch_metadatas.append({
                            'id': node_id,
                            'synonym': synonym,
                            'source': 'synonym'
                        })
                        
                        # 批量添加检查
                        if len(batch_texts) >= batch_size:
                            self.node_store.add_documents(batch_texts, batch_metadatas)
                            total_added += len(batch_texts)
                            pbar.set_postfix({"已添加": total_added})
                            batch_texts.clear()
                            batch_metadatas.clear()
                    
                    pbar.update(1)
            
            # 添加剩余的数据
            if batch_texts:
                self.node_store.add_documents(batch_texts, batch_metadatas)
                total_added += len(batch_texts)
        
        print(f"节点库构建完成！共添加 {total_added} 条记录")
        return self.node_store
    
    def build_all_stores(self, raw_data_path: str, synonyms_path: str, tree_structure_path: str, batch_size: int = 1000):
        """构建所有知识库"""
        print(f"=== 开始构建知识库 (使用{self.vector_store_type.upper()}向量库) ===")
        
        # 加载数据
        self.load_data(raw_data_path, synonyms_path, tree_structure_path)
        
        # 构建语义超图库
        semantic_store = self.build_semantic_hypergraph_store(batch_size=batch_size)
        
        # 构建节点库
        node_store = self.build_node_store(batch_size=batch_size)
        
        print("=== 知识库构建完成 ===")
        
        return semantic_store, node_store
    
    def get_stores(self):
        """获取已构建的存储实例"""
        if self.semantic_hypergraph_store is None:
            self.semantic_hypergraph_store = self._create_vector_store("semantic_hypergraph")
        
        if self.node_store is None:
            self.node_store = self._create_vector_store("node_store")
        
        return self.semantic_hypergraph_store, self.node_store 