import json
import os
from typing import List, Dict, Any

def load_qa_pairs(file_path: str) -> Dict[str, List[str]]:
    """
    从JSON文件加载问题和标准答案

    Args:
        file_path: JSON文件路径

    Returns:
        包含问题列表和标准答案列表的字典
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 检查数据格式
    if isinstance(data, list):
        # 新格式：数组格式，每个元素包含id, type, question, ground_truth_answers
        questions = []
        ground_truth_answers = []

        for item in data:
            if not isinstance(item, dict):
                raise ValueError("数组中的每个元素必须是字典")

            if 'question' not in item or 'ground_truth_answers' not in item:
                raise ValueError("每个元素必须包含'question'和'ground_truth_answers'字段")

            questions.append(item['question'])
            ground_truth_answers.append(item['ground_truth_answers'])

        return {
            'questions': questions,
            'ground_truth_answers': ground_truth_answers
        }

    elif isinstance(data, dict):
        # 旧格式：字典格式，直接包含questions和ground_truth_answers字段
        if 'questions' not in data or 'ground_truth_answers' not in data:
            raise ValueError("JSON格式错误: 必须包含'questions'和'ground_truth_answers'字段")

        if len(data['questions']) != len(data['ground_truth_answers']):
            raise ValueError("问题数量与标准答案数量不匹配")

        return data

    else:
        raise ValueError("JSON格式错误: 数据必须是数组或字典格式")

def load_generated_questions(file_path: str) -> Dict[str, List[str]]:
    """
    从JSON文件加载生成的问题和RAG结果

    Args:
        file_path: RAG结果JSON文件路径

    Returns:
        包含生成答案列表和检索上下文列表的字典
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 检查数据格式
    if isinstance(data, list):
        # 新格式：数组格式，每个元素包含id, question, contexts, answer等
        generated_answers = []
        retrieved_contexts = []

        for item in data:
            if not isinstance(item, dict):
                raise ValueError("数组中的每个元素必须是字典")

            # 提取生成的答案
            if 'answer' in item:
                generated_answers.append(item['answer'])
            else:
                generated_answers.append("")  # 如果没有答案，使用空字符串

            # 提取检索的上下文
            if 'contexts' in item and isinstance(item['contexts'], list):

                context_texts = [json.dumps(c) if isinstance(c, dict) else str(c) for c in item['contexts']]
                retrieved_contexts.append(context_texts)
                # # 将contexts数组转换为字符串列表
                # context_texts = []
                # for context in item['contexts']:
                #     if isinstance(context, dict):
                #         # 提取上下文的描述信息
                #         context_text = ""
                #         if 'name' in context:
                #             context_text += f"Name: {context['name']}\n"
                #         if 'description' in context:
                #             context_text += f"Description: {context['description']}\n"
                #         if 'children' in context:
                #             for child in context['children']:
                #                 if isinstance(child, dict) and 'description' in child:
                #                     context_text += f"Child: {child['description']}\n"
                #         context_texts.append(context_text.strip())
                #     else:
                #         context_texts.append(str(context))

                # retrieved_contexts.append(context_texts)
            else:
                retrieved_contexts.append([])  # 如果没有上下文，使用空列表

        return {
            'generated_answers': generated_answers,
            'retrieved_contexts': retrieved_contexts
        }

    elif isinstance(data, dict):
        # 旧格式：直接返回字典
        return data

    else:
        raise ValueError("JSON格式错误: 数据必须是数组或字典格式")
    


def save_evaluation_results(file_path: str, evaluation_results: Any) -> None:
    """
    将评估结果保存到JSON文件

    Args:
        file_path: 输出JSON文件路径
        evaluation_results: 评估结果对象
    """


    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(str(evaluation_results))

    
    print(f"评估结果已保存到: {file_path}")
