"""
RAG问答系统主入口
"""

import argparse
import json
from service.rag_service import RAGService


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="代谢物RAG问答系统")
    parser.add_argument("--rebuild", action="store_true", help="重新构建知识库")
    parser.add_argument("--query", type=str, help="查询问题")
    parser.add_argument("--interactive", action="store_true", help="交互式模式")
    parser.add_argument("--test", action="store_true", help="运行测试查询")
    parser.add_argument("--stats", action="store_true", help="显示系统统计信息")
    parser.add_argument("--batch-size", type=int, default=1000, help="批处理大小（默认1000）")
    parser.add_argument("--vector-store", type=str, default="chroma", choices=["faiss", "chroma"], help="向量库类型（默认faiss）")
    
    args = parser.parse_args()
    
    # 初始化RAG服务
    rag_service = RAGService(vector_store_type=args.vector_store)
    
    try:
        # 初始化服务
        print("初始化RAG问答系统...")
        rag_service.initialize(rebuild_knowledge_base=args.rebuild, batch_size=args.batch_size)
        
        if args.stats:
            # 显示统计信息
            stats = rag_service.get_statistics()
            print("\n=== 系统统计信息 ===")
            print(json.dumps(stats, indent=2, ensure_ascii=False))
            return
        
        
        if args.query:
            # 单次查询
            print(f"\n=== 查询：{args.query} ===")
            result = rag_service.query(args.query)
            
            if result['success']:
                print(f"\n答案：\n{result['answer']}")
                print(f"\n检索信息：")
                print(f"- 抽取实体：{result['entities']}")
                print(f"- 层级结构：{result['hierarchical_structure_count']}个")
            else:
                print(f"查询失败：{result.get('error', '未知错误')}")
            return
        
        if args.interactive:
            # 交互式模式
            print("\n=== 交互式问答模式 ===")
            print("输入您的问题（输入'quit'或'exit'退出）：")
            
            while True:
                try:
                    user_input = input("\n问题: ").strip()
                    
                    if user_input.lower() in ['quit', 'exit', '退出']:
                        print("感谢使用RAG问答系统！")
                        break
                    
                    if not user_input:
                        continue
                    
                    # 处理查询
                    result = rag_service.query(user_input)
         
                    
                    # if result['success']:
                    #     print(f"\n答案：\n{result['answer']}")
                        
                    #     # 显示详细信息
                    #     if result['entities']['compounds'] or result['entities']['diseases']:
                    #         print(f"\n检索信息：")
                    #         if result['entities']['compounds']:
                    #             print(f"- 检测到化合物：{', '.join(result['entities']['compounds'])}")
                    #         if result['entities']['diseases']:
                    #             print(f"- 检测到疾病：{', '.join(result['entities']['diseases'])}")
                    #         print(f"- 层级结构：{result['hierarchical_structure_count']}个")
                    # else:
                    #     print(f"\n抱歉，查询失败：{result.get('error', '未知错误')}")
                    
                except KeyboardInterrupt:
                    print("\n\n感谢使用RAG问答系统！")
                    break
                except Exception as e:
                    print(f"\n处理查询时出错：{e}")
            return
        
        # 默认显示帮助信息
        print("\n=== RAG问答系统已准备就绪 ===")
        print("使用方式：")
        print("  --query '问题'     : 单次查询")
        print("  --interactive     : 交互式问答")
        print("  --test           : 运行测试查询")
        print("  --rebuild        : 重新构建知识库")
        print("  --stats          : 显示系统统计")
        print("  --vector-store   : 选择向量库类型 (faiss/chroma)")
        print("  --batch-size     : 设置批处理大小")
        print("\n示例：")
        print("  python rag_qa_system.py --query '什么是1-Methylhistidine？'")
        print("  python rag_qa_system.py --interactive --vector-store faiss")
        print("  python rag_qa_system.py --interactive --vector-store chroma")
        print("  python rag_qa_system.py --rebuild --batch-size 500 --vector-store faiss")
        print("  python rag_qa_system.py --rebuild --batch-size 500 --vector-store chroma")
        
    except Exception as e:
        print(f"系统初始化失败：{e}")
        import traceback
        traceback.print_exc()





if __name__ == "__main__":
    main() 