import json

# 1. 输入输出文件名
input_file = "3_results.json"              # 原始数据文件（你已有的）
output_file = "3_results_new.json"   # 清洗后的输出文件

def filter_data(file_path: str) -> list:
    """读取 JSON 文件，过滤掉 answer 为 '抱歉，我无法生成回答。请检查您的问题或稍后再试。' 的项"""
    with open(file_path, "r", encoding="utf-8") as f:
        data = json.load(f)
    
    # 确保是列表格式
    if not isinstance(data, list):
        raise ValueError("JSON 顶层必须是一个列表！")

    # 过滤逻辑：只保留 answer 不等于 "抱歉，我无法生成回答。请检查您的问题或稍后再试。" 的项
    filtered = [item for item in data if item.get("answer") != "抱歉，我无法生成回答。请检查您的问题或稍后再试。"]

    return filtered

def save_data(data: list, file_path: str):
    """将数据保存为 JSON 文件"""
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def main():
    try:
        filtered_data = filter_data(input_file)
        save_data(filtered_data, output_file)
        print(f"过滤完成，结果已保存到：{output_file}")
    except Exception as e:
        print(f"处理失败：{e}")

if __name__ == "__main__":
    main()
