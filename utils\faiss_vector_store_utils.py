import os
import json
import pickle
import uuid
import time
import gc
from typing import List, Dict, Any, Optional
import numpy as np
import faiss
from langchain_huggingface import HuggingFaceEmbeddings
from langchain.schema import Document
import torch
from config import Config


class FAISSVectorStoreUtils:
    """基于FAISS的向量存储工具，支持GPU，针对大规模数据优化"""

    def __init__(self, persist_directory: str, collection_name: str = "default", embedding=None, 
                 use_gpu: bool = False, save_threshold: int = 10000, index_type: str = "ivf"):
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        self.use_gpu = use_gpu
        self.save_threshold = save_threshold  # 延迟保存阈值
        self.index_type = index_type  # 索引类型：ivf, hnsw, flat
        self.pending_saves = 0  # 待保存的文档数量

        # 检测FAISS GPU支持
        self.gpu_available = self._check_gpu_support()
        if use_gpu and not self.gpu_available:
            print("警告: 请求使用GPU但FAISS不支持GPU，将使用CPU版本")
            self.use_gpu = False

        self.embedding = embedding or HuggingFaceEmbeddings(
            model_name=Config.get("EMBEDDING_MODEL_PATH") or "BAAI/bge-base-zh",
            model_kwargs={"device": "cuda" if torch.cuda.is_available() else "cpu"},
            encode_kwargs={"normalize_embeddings": True}
        )

        os.makedirs(persist_directory, exist_ok=True)

        self.index = None
        self.metadatas = {}
        self.id_to_index = {}
        self.index_to_id = {}
        self.dimension = None
        self.current_index = 0

        self.index_file = os.path.join(persist_directory, f"{collection_name}.faiss")
        self.metadata_file = os.path.join(persist_directory, f"{collection_name}_metadata.json")
        self.mapping_file = os.path.join(persist_directory, f"{collection_name}_mapping.pkl")

        # 性能监控
        self.stats = {
            'total_added': 0,
            'total_saves': 0,
            'last_save_time': 0,
            'memory_usage': 0
        }

        self._load_index()

    def _check_gpu_support(self) -> bool:
        """检测FAISS是否支持GPU"""
        try:
            # 检查是否有GPU相关的方法
            if hasattr(faiss, 'get_num_gpus'):
                num_gpus = faiss.get_num_gpus()
                if num_gpus > 0:
                    # 进一步检查GPU转换方法是否存在
                    if hasattr(faiss, 'index_cpu_to_gpu') and hasattr(faiss, 'index_gpu_to_cpu'):
                        print(f"FAISS GPU支持可用，检测到 {num_gpus} 个GPU")
                        return True
                    else:
                        print("FAISS GPU方法不可用，可能是CPU版本")
                        return False
                else:
                    print("未检测到可用的GPU")
                    return False
            else:
                print("FAISS不支持GPU功能")
                return False
        except Exception as e:
            print(f"检测GPU支持时出错: {e}")
            return False

    def _init_index(self, dimension: int):
        """初始化FAISS索引，支持多种索引类型"""
        self.dimension = dimension
        
        if self.index_type == "ivf":
            # IVF索引，适合大规模数据
            nlist = min(4096, max(1, int(np.sqrt(self.dimension * 1000))))  # 聚类中心数量
            quantizer = faiss.IndexFlatIP(dimension)
            cpu_index = faiss.IndexIVFFlat(quantizer, dimension, nlist, faiss.METRIC_INNER_PRODUCT)
            print(f"使用IVF索引，聚类中心数: {nlist}")
        elif self.index_type == "hnsw":
            # HNSW索引，适合高精度搜索
            cpu_index = faiss.IndexHNSWFlat(dimension, 32)  # 32个邻居
            print("使用HNSW索引")
        else:
            # 默认使用Flat索引
            cpu_index = faiss.IndexFlatIP(dimension)
            print("使用Flat索引")

        if self.use_gpu and self.gpu_available:
            print("使用 GPU 加速 FAISS")
            try:
                res = faiss.StandardGpuResources()
                self.index = faiss.index_cpu_to_gpu(res, 0, cpu_index)
            except Exception as e:
                print(f"GPU初始化失败: {e}，回退到CPU版本")
                self.index = cpu_index
                self.use_gpu = False
        else:
            print("使用 CPU 版本 FAISS")
            self.index = cpu_index
            
        print(f"初始化FAISS索引，维度: {dimension}")

    def _load_index(self):
        """加载已存在的FAISS索引，并支持迁移到GPU"""
        try:
            if os.path.exists(self.index_file):
                print(f"加载已存在的FAISS索引: {self.index_file}")
                cpu_index = faiss.read_index(self.index_file)
                self.dimension = cpu_index.d

                if self.use_gpu and self.gpu_available:
                    print("将索引迁移到 GPU")
                    try:
                        res = faiss.StandardGpuResources()
                        self.index = faiss.index_cpu_to_gpu(res, 0, cpu_index)
                    except Exception as e:
                        print(f"GPU迁移失败: {e}，使用CPU版本")
                        self.index = cpu_index
                        self.use_gpu = False
                else:
                    self.index = cpu_index

                if os.path.exists(self.metadata_file):
                    with open(self.metadata_file, 'r', encoding='utf-8') as f:
                        self.metadatas = json.load(f)

                if os.path.exists(self.mapping_file):
                    with open(self.mapping_file, 'rb') as f:
                        mapping_data = pickle.load(f)
                        self.id_to_index = mapping_data.get('id_to_index', {})
                        self.index_to_id = mapping_data.get('index_to_id', {})
                        self.current_index = mapping_data.get('current_index', 0)

                print(f"FAISS索引加载完成，维度: {self.dimension}, 文档数: {len(self.metadatas)}")
        except Exception as e:
            print(f"加载FAISS索引失败: {e}，将创建新索引")
            self.index = None

    def _should_save(self) -> bool:
        """判断是否需要保存索引"""
        return (self.pending_saves >= self.save_threshold or 
                time.time() - self.stats['last_save_time'] > 300)  # 5分钟强制保存

    def _save_index(self, force: bool = False):
        """保存索引到CPU格式并写入磁盘，支持延迟保存"""
        if not force and not self._should_save():
            return

        try:
            if self.index is not None:
                start_time = time.time()
                
                # 检查是否需要GPU到CPU转换
                if self.use_gpu and self.gpu_available:
                    print("将GPU索引迁移到CPU保存")
                    try:
                        cpu_index = faiss.index_gpu_to_cpu(self.index)
                    except Exception as e:
                        print(f"GPU到CPU转换失败: {e}，尝试直接保存")
                        cpu_index = self.index
                else:
                    cpu_index = self.index

                faiss.write_index(cpu_index, self.index_file)

                with open(self.metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(self.metadatas, f, ensure_ascii=False, indent=2)

                mapping_data = {
                    'id_to_index': self.id_to_index,
                    'index_to_id': self.index_to_id,
                    'current_index': self.current_index
                }
                with open(self.mapping_file, 'wb') as f:
                    pickle.dump(mapping_data, f)

                self.pending_saves = 0
                self.stats['total_saves'] += 1
                self.stats['last_save_time'] = time.time()
                
                save_time = time.time() - start_time
                print(f"索引保存完成，耗时: {save_time:.2f}秒")
                
                # 清理内存
                if self.use_gpu and torch.cuda.is_available():
                    torch.cuda.empty_cache()
                gc.collect()
                
        except Exception as e:
            print(f"保存FAISS索引失败: {e}")
            print("建议检查:")
            print("1. 磁盘空间是否充足")
            print("2. 文件权限是否正确")
            print("3. FAISS版本是否兼容")

    def _get_optimal_batch_size(self, current_batch_size: int, processing_time: float) -> int:
        """动态调整批处理大小"""
        if processing_time < 10:  # 处理时间小于10秒，可以增加批次大小
            return min(current_batch_size * 2, 5000)
        elif processing_time > 60:  # 处理时间大于60秒，减少批次大小
            return max(current_batch_size // 2, 100)
        else:
            return current_batch_size

    def add_documents(self, texts: List[str], metadatas: List[dict] = None) -> List[str]:
        if not texts:
            return []

        metadatas = metadatas or [{}] * len(texts)
        start_time = time.time()

        print(f"正在生成 {len(texts)} 个文档的向量...")
        embeddings = self.embedding.embed_documents(texts)
        embeddings = np.array(embeddings, dtype=np.float32)

        if self.index is None:
            self._init_index(embeddings.shape[1])

        doc_ids = [str(uuid.uuid4()) for _ in texts]
        
        # 对于IVF索引，需要先训练
        if self.index_type == "ivf" and not self.index.is_trained:
            print("训练IVF索引...")
            self.index.train(embeddings)
        
        self.index.add(embeddings)

        for i, (doc_id, text, metadata) in enumerate(zip(doc_ids, texts, metadatas)):
            faiss_index = self.current_index + i
            self.metadatas[doc_id] = {'text': text, **metadata}
            self.id_to_index[doc_id] = faiss_index
            self.index_to_id[faiss_index] = doc_id

        self.current_index += len(texts)
        self.pending_saves += len(texts)
        self.stats['total_added'] += len(texts)

        # 延迟保存
        self._save_index()

        processing_time = time.time() - start_time
        print(f"添加 {len(texts)} 个文档完成，耗时: {processing_time:.2f}秒")
        
        # 更新内存使用统计
        if torch.cuda.is_available():
            self.stats['memory_usage'] = torch.cuda.memory_allocated() / 1024**3  # GB

        return doc_ids

    def search(self, query: str, k: int = 4, with_score: bool = False, level_filter: Optional[int] = None) -> List[Document]:
        if self.index is None or self.index.ntotal == 0:
            return []

        query_embedding = self.embedding.embed_query(query)
        query_vector = np.array([query_embedding], dtype=np.float32)

        # 自适应搜索范围设置
        initial_search_k = k * 3
        max_search_k = self.index.ntotal # 最大搜索所有文档
        current_search_k = initial_search_k
        
        results = []
        searched_count = 0
        search_attempts = 0

        while len(results) < k :
            # 执行向量搜索
            scores, indices = self.index.search(query_vector, min(current_search_k, self.index.ntotal))
            
            # 处理搜索结果
            for score, idx in zip(scores[0], indices[0]):
                if idx == -1:
                    continue
      
                    
                doc_id = self.index_to_id.get(idx)
                if doc_id and doc_id in self.metadatas:
                    metadata = self.metadatas[doc_id].copy()
                    text = metadata.pop('text', '')

                    # 应用level过滤
                    if level_filter is not None:
                        doc_level = metadata.get('level')
                        if doc_level != level_filter:
                            continue

                    doc = Document(page_content=text, metadata=metadata)
                    results.append((doc, float(score)) if with_score else doc)

                    if len(results) >= k:
                        break
            
            searched_count = len(scores[0])
            search_attempts += 1
            
            # 如果还没找到足够的文档，扩大搜索范围
            if len(results) < k and current_search_k < max_search_k:
                current_search_k = min(current_search_k * 2, max_search_k)
                if level_filter is not None:
                    print(f"Level {level_filter} 文档不足，第 {search_attempts} 次尝试，扩大搜索范围到 {current_search_k} 个文档")
            elif len(results) < k and current_search_k >= max_search_k:
                # 已经达到最大搜索范围，退出循环
                if level_filter is not None:
                    print(f"已达到最大搜索范围 {max_search_k}，但只找到 {len(results)} 个 level {level_filter} 文档")
                break
        
        # 输出搜索统计信息
        if level_filter is not None and searched_count > 0:
            print(f"搜索统计: 请求 {k} 个 level {level_filter} 文档，尝试 {search_attempts} 次，实际搜索了 {searched_count} 个文档，找到 {len(results)} 个结果")
        
        return results

    def delete_document(self, doc_id: str):
        if doc_id in self.metadatas:
            del self.metadatas[doc_id]

        if doc_id in self.id_to_index:
            faiss_idx = self.id_to_index[doc_id]
            del self.id_to_index[doc_id]
            if faiss_idx in self.index_to_id:
                del self.index_to_id[faiss_idx]

        self._save_index(force=True)

    def update_document(self, doc_id: str, new_text: str, new_metadata: dict = None):
        self.delete_document(doc_id)
        self.add_documents([new_text], [new_metadata or {}])

    def get_stats(self) -> Dict[str, Any]:
        return {
            'total_documents': len(self.metadatas),
            'faiss_total': self.index.ntotal if self.index else 0,
            'dimension': self.dimension,
            'index_type': str(type(self.index)) if self.index else None,
            'pending_saves': self.pending_saves,
            'total_saves': self.stats['total_saves'],
            'memory_usage_gb': self.stats['memory_usage'],
            'save_threshold': self.save_threshold,
            'gpu_available': self.gpu_available,
            'use_gpu': self.use_gpu
        }

    def force_save(self):
        """强制保存索引"""
        self._save_index(force=True)

    def rebuild_index(self):
        if not self.metadatas:
            return

        print("重建FAISS索引以清理已删除的文档...")

        valid_texts = []
        valid_metadatas = []
        valid_ids = []

        for doc_id, metadata in self.metadatas.items():
            if doc_id in self.id_to_index:
                valid_texts.append(metadata['text'])
                valid_metadatas.append({k: v for k, v in metadata.items() if k != 'text'})
                valid_ids.append(doc_id)

        self.index = None
        self.metadatas.clear()
        self.id_to_index.clear()
        self.index_to_id.clear()
        self.current_index = 0
        self.pending_saves = 0

        if valid_texts:
            self.add_documents(valid_texts, valid_metadatas)

        print(f"索引重建完成，剩余文档数: {len(valid_texts)}")
