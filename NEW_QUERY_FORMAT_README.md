# 新检索逻辑实现说明

## 概述

已成功实现新的检索逻辑，支持更精确的层级查询格式。

## 新的输入格式

```python
[
    {'id': 8228, 'query': [1, 3]}, 
    {'id': 8227, 'query': [1, 3]}, 
    {'id': 7936, 'query': [2]}
]
```

### 格式说明

- `id`: 节点ID
- `query`: 需要检索的层级列表
  - `1`: 检索第1层（疾病层）信息
  - `2`: 检索第2层（代谢物层）信息  
  - `3`: 检索第3层（浓度数据层）信息

### 示例解释

- `{'id': 8228, 'query': [1, 3]}`: 检索ID为8228的节点的第1层和第3层信息（包括节点自身）
- `{'id': 8227, 'query': [1, 3]}`: 检索ID为8227的节点的第1层和第3层信息（包括节点自身）
- `{'id': 7936, 'query': [2]}`: 检索ID为7936的节点的第2层信息（包括节点自身）

## 实现细节

### 新增方法

#### `_build_hierarchical_knowledge_by_query(self, query_data: list) -> list`

这是新的核心方法，用于处理新的查询格式。

**功能特点：**

1. **精确控制**: 只检索query字段中指定的层级信息
2. **包含自身**: 总是包含节点自身的信息
3. **智能查找**: 根据节点当前层级智能查找相关层级的节点
4. **去重处理**: 自动去重，避免重复的节点信息

**查询逻辑：**

- **查询Level 1**: 
  - 如果节点本身是Level 1，直接包含
  - 否则查找其Level 1祖先节点

- **查询Level 2**:
  - 如果节点本身是Level 2，直接包含
  - 如果是Level 1节点，查找其Level 2子节点
  - 如果是Level 3节点，查找其Level 2父节点

- **查询Level 3**:
  - 如果节点本身是Level 3，直接包含
  - 如果是Level 2节点，查找其Level 3子节点
  - 如果是Level 1节点，查找所有Level 2子节点的Level 3子节点

### 辅助方法

#### `_find_ancestor_by_level(self, node_id: int, target_level: int) -> Optional[int]`

查找指定节点的指定层级祖先节点。

#### `_build_hierarchical_relationships(self, result_dict: OrderedDict)`

构建节点之间的层级关系，建立父子关系。

#### `_extract_root_nodes(self, result_dict: OrderedDict) -> list`

提取根节点，优先级：Level 1 > Level 2 > Level 3。

## 代码更改

### 主要更改

1. **新增导入**: 添加了 `from collections import OrderedDict`
2. **新增方法**: 实现了 `_build_hierarchical_knowledge_by_query` 及其辅助方法
3. **更新调用**: 将 `query` 方法中的调用从 `_build_hierarchical_knowledge_new` 改为 `_build_hierarchical_knowledge_by_query`

### 文件位置

- 主要修改文件: `service/rag_service.py`
- 修改行数: 约130行新增代码

## 优势

1. **更精确**: 只检索用户真正需要的层级信息
2. **更高效**: 避免不必要的数据检索和处理
3. **更灵活**: 支持任意层级组合的查询
4. **向后兼容**: 保留了原有的方法，不影响现有功能

## 使用方式

新的查询格式会自动被 `decision_path` 方法返回，然后由 `_build_hierarchical_knowledge_by_query` 方法处理。用户无需修改调用方式，系统会自动使用新的逻辑。

## 测试

已通过语法检查，代码结构正确。建议在完整环境中进行功能测试以验证实际效果。
