"""
构建代谢物多层树结构的主执行脚本
"""

import os
import sys
import time
from service import MetaboliteTreeService
from config import Config

def main():
    """主函数"""
    print("=== 代谢物多层树结构构建器 ===")
    
    # 输入和输出路径配置
    input_file = Config.get("ORI_DATA_PATH")
    output_dir = Config.get("MS_TREE_PATH")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：输入文件 {input_file} 不存在")
        return False
    
    # 获取文件大小信息
    file_size = os.path.getsize(input_file) / (1024 * 1024)  # MB
    print(f"输入文件: {input_file}")
    print(f"文件大小: {file_size:.1f} MB")
    print(f"输出目录: {output_dir}")
    
    # 创建服务实例
    print("\n正在初始化树服务...")
    tree_service = MetaboliteTreeService()
    
    # 开始构建树结构
    print("\n开始构建树结构...")
    start_time = time.time()
    
    try:
        # 构建树结构
        success = tree_service.build_tree_from_json(input_file)
        
        if not success:
            print("树结构构建失败！")
            return False
        
        # 保存结果到文件
        print("\n正在保存结果...")
        tree_service.save_to_files(output_dir)
        
        # 计算耗时
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n=== 构建完成 ===")
        print(f"总耗时: {duration:.2f} 秒")
        print(f"结果已保存到目录: {os.path.abspath(output_dir)}")
        
        # 输出生成的文件列表
        print(f"\n生成的文件:")
        output_files = [
            "raw_data.json",
            "tree_structure.json", 
            "synonyms_mapping.json"
        ]
        
        for filename in output_files:
            filepath = os.path.join(output_dir, filename)
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath) / (1024 * 1024)
                print(f"  - {filename} ({file_size:.1f} MB)")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        return False
    except Exception as e:
        print(f"\n构建过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False



if __name__ == "__main__":
   
    # 运行完整构建
    success = main()
    sys.exit(0 if success else 1) 